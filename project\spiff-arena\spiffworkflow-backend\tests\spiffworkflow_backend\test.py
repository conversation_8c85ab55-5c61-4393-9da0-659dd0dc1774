import os
import time
import json

from alibabacloud_dysmsapi20170525.client import Client as DysmsClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysms_models
from alibabacloud_tea_util import models as util_models

class SMSService:
    def __init__(self):
        self.client = self.create_client()
    
    def create_client(self) -> DysmsClient:
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        config = open_api_models.Config(
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。,
            access_key_id=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'],
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。,
            access_key_secret=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']
        )
        config.endpoint = f'dysmsapi.aliyuncs.com'
        return DysmsClient(config)
    
    def send_sms(self, phone_numbers: str, sign_name: str, template_code: str, template_param: str = None) -> object:
        """
        发送短信服务
        :param phone_numbers: 接收短信的手机号码
        :param sign_name: 短信签名
        :param template_code: 短信模板code
        :param template_param: 模板参数,JSON格式字符串
        :return: 发送结果
        """
        send_sms_request = dysms_models.SendSmsRequest(
            phone_numbers=phone_numbers,
            sign_name=sign_name,
            template_code=template_code,
            template_param=template_param
        )

        # 创建运行时参数
        runtime = util_models.RuntimeOptions()

        # 发起请求并返回结果
        return self.client.send_sms_with_options(send_sms_request, runtime)

# 设置环境变量（请替换为您的真实AccessKey）
os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'] = 'LTAI5t986sJDqpdCa5VJ5MwJ'
os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET'] = '******************************'

# 创建短信服务实例
try:
    sms_service = SMSService()
    print("短信服务客户端创建成功")

    # 发送短信测试
    response = sms_service.send_sms(
        phone_numbers="18258843019",
        sign_name="阿里云短信测试",
        template_code="SMS_154950909",
        template_param='{"code":"123456"}'
    )
    
    print("发送结果:")
    print(response)
    
    # 检查响应内容
    if hasattr(response, 'body') and hasattr(response.body, 'code'):
        if response.body.code == 'OK':
            print("短信发送成功!")
        else:
            print(f"短信发送失败: {response.body.message}")
    else:
        print("无法解析响应内容")
        
except Exception as e:
    print(f"发生错误: {str(e)}")