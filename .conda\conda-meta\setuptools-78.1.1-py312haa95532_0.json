{"build": "py312haa95532_0", "build_number": 0, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/noarch", "constrains": [], "depends": ["python >=3.12,<3.13.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py312haa95532_0", "files": ["Lib/site-packages/_distutils_hack/__init__.py", "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-312.pyc", "Lib/site-packages/_distutils_hack/override.py", "Lib/site-packages/distutils-precedence.pth", "Lib/site-packages/pkg_resources/__init__.py", "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/pkg_resources/api_tests.txt", "Lib/site-packages/pkg_resources/py.typed", "Lib/site-packages/pkg_resources/tests/__init__.py", "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-312.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "Lib/site-packages/pkg_resources/tests/test_markers.py", "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "Lib/site-packages/pkg_resources/tests/test_resources.py", "Lib/site-packages/pkg_resources/tests/test_working_set.py", "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/PKG-INFO", "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/SOURCES.txt", "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/dependency_links.txt", "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/entry_points.txt", "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/requires.txt", "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/top_level.txt", "Lib/site-packages/setuptools/__init__.py", "Lib/site-packages/setuptools/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_imp.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_path.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/_static.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/depends.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/discovery.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/dist.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/errors.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/extension.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/glob.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/installer.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/launch.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/logging.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/modified.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/monkey.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/msvc.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/package_index.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/version.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/warnings.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/wheel.cpython-312.pyc", "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-312.pyc", "Lib/site-packages/setuptools/_core_metadata.py", "Lib/site-packages/setuptools/_distutils/__init__.py", "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/_log.py", "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "Lib/site-packages/setuptools/_distutils/_modified.py", "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/archive_util.py", "Lib/site-packages/setuptools/_distutils/ccompiler.py", "Lib/site-packages/setuptools/_distutils/cmd.py", "Lib/site-packages/setuptools/_distutils/command/__init__.py", "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "Lib/site-packages/setuptools/_distutils/command/bdist.py", "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/command/build.py", "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "Lib/site-packages/setuptools/_distutils/command/build_py.py", "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "Lib/site-packages/setuptools/_distutils/command/check.py", "Lib/site-packages/setuptools/_distutils/command/clean.py", "Lib/site-packages/setuptools/_distutils/command/config.py", "Lib/site-packages/setuptools/_distutils/command/install.py", "Lib/site-packages/setuptools/_distutils/command/install_data.py", "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "Lib/site-packages/setuptools/_distutils/command/sdist.py", "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "Lib/site-packages/setuptools/_distutils/compat/py39.py", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "Lib/site-packages/setuptools/_distutils/core.py", "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/debug.py", "Lib/site-packages/setuptools/_distutils/dep_util.py", "Lib/site-packages/setuptools/_distutils/dir_util.py", "Lib/site-packages/setuptools/_distutils/dist.py", "Lib/site-packages/setuptools/_distutils/errors.py", "Lib/site-packages/setuptools/_distutils/extension.py", "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "Lib/site-packages/setuptools/_distutils/file_util.py", "Lib/site-packages/setuptools/_distutils/filelist.py", "Lib/site-packages/setuptools/_distutils/log.py", "Lib/site-packages/setuptools/_distutils/spawn.py", "Lib/site-packages/setuptools/_distutils/sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-312.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "Lib/site-packages/setuptools/_distutils/tests/support.py", "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "Lib/site-packages/setuptools/_distutils/text_file.py", "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "Lib/site-packages/setuptools/_distutils/util.py", "Lib/site-packages/setuptools/_distutils/version.py", "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "Lib/site-packages/setuptools/_entry_points.py", "Lib/site-packages/setuptools/_imp.py", "Lib/site-packages/setuptools/_importlib.py", "Lib/site-packages/setuptools/_itertools.py", "Lib/site-packages/setuptools/_normalization.py", "Lib/site-packages/setuptools/_path.py", "Lib/site-packages/setuptools/_reqs.py", "Lib/site-packages/setuptools/_shutil.py", "Lib/site-packages/setuptools/_static.py", "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/packaging/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "Lib/site-packages/setuptools/_vendor/wheel/util.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-312.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "Lib/site-packages/setuptools/archive_util.py", "Lib/site-packages/setuptools/build_meta.py", "Lib/site-packages/setuptools/cli-32.exe", "Lib/site-packages/setuptools/cli-64.exe", "Lib/site-packages/setuptools/cli-arm64.exe", "Lib/site-packages/setuptools/cli.exe", "Lib/site-packages/setuptools/command/__init__.py", "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/build.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/install.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-312.pyc", "Lib/site-packages/setuptools/command/__pycache__/test.cpython-312.pyc", "Lib/site-packages/setuptools/command/_requirestxt.py", "Lib/site-packages/setuptools/command/alias.py", "Lib/site-packages/setuptools/command/bdist_egg.py", "Lib/site-packages/setuptools/command/bdist_rpm.py", "Lib/site-packages/setuptools/command/bdist_wheel.py", "Lib/site-packages/setuptools/command/build.py", "Lib/site-packages/setuptools/command/build_clib.py", "Lib/site-packages/setuptools/command/build_ext.py", "Lib/site-packages/setuptools/command/build_py.py", "Lib/site-packages/setuptools/command/develop.py", "Lib/site-packages/setuptools/command/dist_info.py", "Lib/site-packages/setuptools/command/easy_install.py", "Lib/site-packages/setuptools/command/editable_wheel.py", "Lib/site-packages/setuptools/command/egg_info.py", "Lib/site-packages/setuptools/command/install.py", "Lib/site-packages/setuptools/command/install_egg_info.py", "Lib/site-packages/setuptools/command/install_lib.py", "Lib/site-packages/setuptools/command/install_scripts.py", "Lib/site-packages/setuptools/command/launcher manifest.xml", "Lib/site-packages/setuptools/command/rotate.py", "Lib/site-packages/setuptools/command/saveopts.py", "Lib/site-packages/setuptools/command/sdist.py", "Lib/site-packages/setuptools/command/setopt.py", "Lib/site-packages/setuptools/command/test.py", "Lib/site-packages/setuptools/compat/__init__.py", "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-312.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-312.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-312.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-312.pyc", "Lib/site-packages/setuptools/compat/py310.py", "Lib/site-packages/setuptools/compat/py311.py", "Lib/site-packages/setuptools/compat/py312.py", "Lib/site-packages/setuptools/compat/py39.py", "Lib/site-packages/setuptools/config/NOTICE", "Lib/site-packages/setuptools/config/__init__.py", "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-312.pyc", "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-312.pyc", "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-312.pyc", "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-312.pyc", "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-312.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-312.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-312.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-312.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-312.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "Lib/site-packages/setuptools/config/distutils.schema.json", "Lib/site-packages/setuptools/config/expand.py", "Lib/site-packages/setuptools/config/pyprojecttoml.py", "Lib/site-packages/setuptools/config/setupcfg.py", "Lib/site-packages/setuptools/config/setuptools.schema.json", "Lib/site-packages/setuptools/depends.py", "Lib/site-packages/setuptools/discovery.py", "Lib/site-packages/setuptools/dist.py", "Lib/site-packages/setuptools/errors.py", "Lib/site-packages/setuptools/extension.py", "Lib/site-packages/setuptools/glob.py", "Lib/site-packages/setuptools/gui-32.exe", "Lib/site-packages/setuptools/gui-64.exe", "Lib/site-packages/setuptools/gui-arm64.exe", "Lib/site-packages/setuptools/gui.exe", "Lib/site-packages/setuptools/installer.py", "Lib/site-packages/setuptools/launch.py", "Lib/site-packages/setuptools/logging.py", "Lib/site-packages/setuptools/modified.py", "Lib/site-packages/setuptools/monkey.py", "Lib/site-packages/setuptools/msvc.py", "Lib/site-packages/setuptools/namespaces.py", "Lib/site-packages/setuptools/package_index.py", "Lib/site-packages/setuptools/sandbox.py", "Lib/site-packages/setuptools/script (dev).tmpl", "Lib/site-packages/setuptools/script.tmpl", "Lib/site-packages/setuptools/tests/__init__.py", "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-312.pyc", "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-312.pyc", "Lib/site-packages/setuptools/tests/compat/__init__.py", "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-312.pyc", "Lib/site-packages/setuptools/tests/compat/py39.py", "Lib/site-packages/setuptools/tests/config/__init__.py", "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-312.pyc", "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_expand.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "Lib/site-packages/setuptools/tests/contexts.py", "Lib/site-packages/setuptools/tests/environment.py", "Lib/site-packages/setuptools/tests/fixtures.py", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "Lib/site-packages/setuptools/tests/integration/__init__.py", "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-312.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-312.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-312.pyc", "Lib/site-packages/setuptools/tests/integration/helpers.py", "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "Lib/site-packages/setuptools/tests/mod_with_constant.py", "Lib/site-packages/setuptools/tests/namespaces.py", "Lib/site-packages/setuptools/tests/script-with-bom.py", "Lib/site-packages/setuptools/tests/server.py", "Lib/site-packages/setuptools/tests/test_archive_util.py", "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "Lib/site-packages/setuptools/tests/test_build.py", "Lib/site-packages/setuptools/tests/test_build_clib.py", "Lib/site-packages/setuptools/tests/test_build_ext.py", "Lib/site-packages/setuptools/tests/test_build_meta.py", "Lib/site-packages/setuptools/tests/test_build_py.py", "Lib/site-packages/setuptools/tests/test_config_discovery.py", "Lib/site-packages/setuptools/tests/test_core_metadata.py", "Lib/site-packages/setuptools/tests/test_depends.py", "Lib/site-packages/setuptools/tests/test_develop.py", "Lib/site-packages/setuptools/tests/test_dist.py", "Lib/site-packages/setuptools/tests/test_dist_info.py", "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "Lib/site-packages/setuptools/tests/test_easy_install.py", "Lib/site-packages/setuptools/tests/test_editable_install.py", "Lib/site-packages/setuptools/tests/test_egg_info.py", "Lib/site-packages/setuptools/tests/test_extern.py", "Lib/site-packages/setuptools/tests/test_find_packages.py", "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "Lib/site-packages/setuptools/tests/test_glob.py", "Lib/site-packages/setuptools/tests/test_install_scripts.py", "Lib/site-packages/setuptools/tests/test_logging.py", "Lib/site-packages/setuptools/tests/test_manifest.py", "Lib/site-packages/setuptools/tests/test_namespaces.py", "Lib/site-packages/setuptools/tests/test_packageindex.py", "Lib/site-packages/setuptools/tests/test_sandbox.py", "Lib/site-packages/setuptools/tests/test_sdist.py", "Lib/site-packages/setuptools/tests/test_setopt.py", "Lib/site-packages/setuptools/tests/test_setuptools.py", "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "Lib/site-packages/setuptools/tests/test_virtualenv.py", "Lib/site-packages/setuptools/tests/test_warnings.py", "Lib/site-packages/setuptools/tests/test_wheel.py", "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "Lib/site-packages/setuptools/tests/text.py", "Lib/site-packages/setuptools/tests/textwrap.py", "Lib/site-packages/setuptools/unicode_utils.py", "Lib/site-packages/setuptools/version.py", "Lib/site-packages/setuptools/warnings.py", "Lib/site-packages/setuptools/wheel.py", "Lib/site-packages/setuptools/windows_support.py"], "fn": "setuptools-78.1.1-py312haa95532_0.conda", "license": "MIT", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py312haa95532_0", "type": 1}, "md5": "05c03a4a6d86fb39aefb8ea15571e94b", "name": "setuptools", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py312haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "sha256_in_prefix": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "size_in_bytes": 6755}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "f692e6c872a46bcc62589ec5a699d762f840a5c8371c9a0d8900eda146eadbbd", "sha256_in_prefix": "f692e6c872a46bcc62589ec5a699d762f840a5c8371c9a0d8900eda146eadbbd", "size_in_bytes": 10547}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-312.pyc", "path_type": "hardlink", "sha256": "0ed4387694dc95d9025b3fe7d8a231ade48b7f19f73a6aafa6390c311a7e1467", "sha256_in_prefix": "0ed4387694dc95d9025b3fe7d8a231ade48b7f19f73a6aafa6390c311a7e1467", "size_in_bytes": 243}, {"_path": "Lib/site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "Lib/site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "sha256_in_prefix": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "size_in_bytes": 152}, {"_path": "Lib/site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "sha256_in_prefix": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "size_in_bytes": 126203}, {"_path": "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "cb948b87d23c024073fddbd547a64ef9a4dae67c74791460958eb08361e1d949", "sha256_in_prefix": "cb948b87d23c024073fddbd547a64ef9a4dae67c74791460958eb08361e1d949", "size_in_bytes": 164377}, {"_path": "Lib/site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "Lib/site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "88ca5c442160bd885809e686af8bc6092e54b7918a5b18827c22fba371cfc72f", "sha256_in_prefix": "88ca5c442160bd885809e686af8bc6092e54b7918a5b18827c22fba371cfc72f", "size_in_bytes": 146}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-312.pyc", "path_type": "hardlink", "sha256": "26e02c80bb8ffc5ffed0ab2db679afc12d8d7a40d449f43130109cb6c5976878", "sha256_in_prefix": "26e02c80bb8ffc5ffed0ab2db679afc12d8d7a40d449f43130109cb6c5976878", "size_in_bytes": 3259}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-312.pyc", "path_type": "hardlink", "sha256": "794494b6957913a48e2d935f1ef91d84eb6979f988ba722176d041e2358c200e", "sha256_in_prefix": "794494b6957913a48e2d935f1ef91d84eb6979f988ba722176d041e2358c200e", "size_in_bytes": 1956}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-312.pyc", "path_type": "hardlink", "sha256": "985a28e7e4bf6f6a3448aaca7bcdc07a5027f37fd171a230c3d7231eebf85d44", "sha256_in_prefix": "985a28e7e4bf6f6a3448aaca7bcdc07a5027f37fd171a230c3d7231eebf85d44", "size_in_bytes": 583}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-312.pyc", "path_type": "hardlink", "sha256": "32ea4b14adea3670af63aa7633f1e82e62cc000d2f41866f29f25ded217ab5cc", "sha256_in_prefix": "32ea4b14adea3670af63aa7633f1e82e62cc000d2f41866f29f25ded217ab5cc", "size_in_bytes": 25148}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-312.pyc", "path_type": "hardlink", "sha256": "ea218cf237e3a2823c8d3df822fabe63763b8afe14c229e269df5275c0fe1d7d", "sha256_in_prefix": "ea218cf237e3a2823c8d3df822fabe63763b8afe14c229e269df5275c0fe1d7d", "size_in_bytes": 47353}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-312.pyc", "path_type": "hardlink", "sha256": "9a83d29e3dd020bd47d0316485f26a7850fa997652bd3a02e7cf7f481c56db4d", "sha256_in_prefix": "9a83d29e3dd020bd47d0316485f26a7850fa997652bd3a02e7cf7f481c56db4d", "size_in_bytes": 10452}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-312.pyc", "path_type": "hardlink", "sha256": "c406f131c52b0a439ded794fe96004a30b42ef114f2eb046ee98b188769c3096", "sha256_in_prefix": "c406f131c52b0a439ded794fe96004a30b42ef114f2eb046ee98b188769c3096", "size_in_bytes": 320}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "Lib/site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "sha256_in_prefix": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "size_in_bytes": 17111}, {"_path": "Lib/site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "sha256_in_prefix": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "size_in_bytes": 31252}, {"_path": "Lib/site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "sha256_in_prefix": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "size_in_bytes": 8602}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "sha256_in_prefix": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "size_in_bytes": 6688}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "sha256_in_prefix": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "size_in_bytes": 24294}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/requires.txt", "path_type": "hardlink", "sha256": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "sha256_in_prefix": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "size_in_bytes": 1231}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.12.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "Lib/site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "sha256_in_prefix": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "size_in_bytes": 10406}, {"_path": "Lib/site-packages/setuptools/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "cef4582a383eb3cbe94a16ed238141585b5f8b616ec10ede5e3e7746696e07d3", "sha256_in_prefix": "cef4582a383eb3cbe94a16ed238141585b5f8b616ec10ede5e3e7746696e07d3", "size_in_bytes": 13867}, {"_path": "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-312.pyc", "path_type": "hardlink", "sha256": "ab481929980b606c59b635dce4aae46e9a603d8f65d95349bc6fc3d2c4207cdd", "sha256_in_prefix": "ab481929980b606c59b635dce4aae46e9a603d8f65d95349bc6fc3d2c4207cdd", "size_in_bytes": 14867}, {"_path": "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-312.pyc", "path_type": "hardlink", "sha256": "7ad25fba16741cbd4fbb15a6ee8f9872150aafdd5cc6457ad3809d808808dde3", "sha256_in_prefix": "7ad25fba16741cbd4fbb15a6ee8f9872150aafdd5cc6457ad3809d808808dde3", "size_in_bytes": 4639}, {"_path": "Lib/site-packages/setuptools/__pycache__/_imp.cpython-312.pyc", "path_type": "hardlink", "sha256": "16abd69d28417e5b2c4ec3e367c5b1befb6d9b67bcaef0bf72a3d23224ad16f7", "sha256_in_prefix": "16abd69d28417e5b2c4ec3e367c5b1befb6d9b67bcaef0bf72a3d23224ad16f7", "size_in_bytes": 3569}, {"_path": "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-312.pyc", "path_type": "hardlink", "sha256": "aa253ea11e74d8bd60a8d362da8ee04f9e494be07fda6a169f6ab637992c8f9b", "sha256_in_prefix": "aa253ea11e74d8bd60a8d362da8ee04f9e494be07fda6a169f6ab637992c8f9b", "size_in_bytes": 354}, {"_path": "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-312.pyc", "path_type": "hardlink", "sha256": "06889bfba72b0f207d0582496f38a7a1f04df72ebb721746389b07076772c010", "sha256_in_prefix": "06889bfba72b0f207d0582496f38a7a1f04df72ebb721746389b07076772c010", "size_in_bytes": 1008}, {"_path": "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-312.pyc", "path_type": "hardlink", "sha256": "0aa9a66ff7d088047bfc68e8e76a3e60f56e8a1b4c739752e388aef43e49b454", "sha256_in_prefix": "0aa9a66ff7d088047bfc68e8e76a3e60f56e8a1b4c739752e388aef43e49b454", "size_in_bytes": 7371}, {"_path": "Lib/site-packages/setuptools/__pycache__/_path.cpython-312.pyc", "path_type": "hardlink", "sha256": "c5141e3e54835d0c00422ca65a5fe393bccf96566219a499da879c5095a7e0a0", "sha256_in_prefix": "c5141e3e54835d0c00422ca65a5fe393bccf96566219a499da879c5095a7e0a0", "size_in_bytes": 4151}, {"_path": "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-312.pyc", "path_type": "hardlink", "sha256": "128f1ddb85d631e99b94d93cd4a4e6b7e7b4cd6003a466bea63a11980efbbd34", "sha256_in_prefix": "128f1ddb85d631e99b94d93cd4a4e6b7e7b4cd6003a466bea63a11980efbbd34", "size_in_bytes": 1920}, {"_path": "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-312.pyc", "path_type": "hardlink", "sha256": "916d9fb6163e0fb86e24132b02c5bed1d5d2b711ee5ccaa416484c09bb628030", "sha256_in_prefix": "916d9fb6163e0fb86e24132b02c5bed1d5d2b711ee5ccaa416484c09bb628030", "size_in_bytes": 2305}, {"_path": "Lib/site-packages/setuptools/__pycache__/_static.cpython-312.pyc", "path_type": "hardlink", "sha256": "41c8d4913807afa9e2db4132d002fe2633eed077e4d98830fd541708445835f8", "sha256_in_prefix": "41c8d4913807afa9e2db4132d002fe2633eed077e4d98830fd541708445835f8", "size_in_bytes": 6006}, {"_path": "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "9a3e8387d74aef9e9bee1d103c884d23c57b789f980069d2bddc5fdaf3c23b94", "sha256_in_prefix": "9a3e8387d74aef9e9bee1d103c884d23c57b789f980069d2bddc5fdaf3c23b94", "size_in_bytes": 9154}, {"_path": "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-312.pyc", "path_type": "hardlink", "sha256": "68926112bc946305789d12d24e73552d0595ec3211bf5d4a2ba9adaffe3b8653", "sha256_in_prefix": "68926112bc946305789d12d24e73552d0595ec3211bf5d4a2ba9adaffe3b8653", "size_in_bytes": 24993}, {"_path": "Lib/site-packages/setuptools/__pycache__/depends.cpython-312.pyc", "path_type": "hardlink", "sha256": "ef9941daa728dae4fce6babbd743a0708b278f61c6b6f4a723d86e7e748e477a", "sha256_in_prefix": "ef9941daa728dae4fce6babbd743a0708b278f61c6b6f4a723d86e7e748e477a", "size_in_bytes": 7558}, {"_path": "Lib/site-packages/setuptools/__pycache__/discovery.cpython-312.pyc", "path_type": "hardlink", "sha256": "2b7245c679b76932976a25957a3b38666fe73dfde6513abceffbd7ebf0ecfe74", "sha256_in_prefix": "2b7245c679b76932976a25957a3b38666fe73dfde6513abceffbd7ebf0ecfe74", "size_in_bytes": 28639}, {"_path": "Lib/site-packages/setuptools/__pycache__/dist.cpython-312.pyc", "path_type": "hardlink", "sha256": "4a787a6170365423549018efc2f5401b46db400186daadd55cd1a30e895cbf18", "sha256_in_prefix": "4a787a6170365423549018efc2f5401b46db400186daadd55cd1a30e895cbf18", "size_in_bytes": 52100}, {"_path": "Lib/site-packages/setuptools/__pycache__/errors.cpython-312.pyc", "path_type": "hardlink", "sha256": "b1f61502a96b269ec2943224eb87abff446e04c76c8ba4f7f7fb851fb8d07e30", "sha256_in_prefix": "b1f61502a96b269ec2943224eb87abff446e04c76c8ba4f7f7fb851fb8d07e30", "size_in_bytes": 3450}, {"_path": "Lib/site-packages/setuptools/__pycache__/extension.cpython-312.pyc", "path_type": "hardlink", "sha256": "8a1b380d53bbf9e415de3e803d251440cb53d4237a502857f0dc64ffb9a38d3a", "sha256_in_prefix": "8a1b380d53bbf9e415de3e803d251440cb53d4237a502857f0dc64ffb9a38d3a", "size_in_bytes": 6862}, {"_path": "Lib/site-packages/setuptools/__pycache__/glob.cpython-312.pyc", "path_type": "hardlink", "sha256": "01096d6c8e23735cf8ab254740e77cae0def61f63acd476cc304e580010d07b5", "sha256_in_prefix": "01096d6c8e23735cf8ab254740e77cae0def61f63acd476cc304e580010d07b5", "size_in_bytes": 7378}, {"_path": "Lib/site-packages/setuptools/__pycache__/installer.cpython-312.pyc", "path_type": "hardlink", "sha256": "0b42f7a5c881c7f41badeeec40b66a0cea6da138576729d508111dae39e6497e", "sha256_in_prefix": "0b42f7a5c881c7f41badeeec40b66a0cea6da138576729d508111dae39e6497e", "size_in_bytes": 6549}, {"_path": "Lib/site-packages/setuptools/__pycache__/launch.cpython-312.pyc", "path_type": "hardlink", "sha256": "71473a872e0ea603f5b75d5d6defe402dee73552442878cb3f631b371a1aec83", "sha256_in_prefix": "71473a872e0ea603f5b75d5d6defe402dee73552442878cb3f631b371a1aec83", "size_in_bytes": 1271}, {"_path": "Lib/site-packages/setuptools/__pycache__/logging.cpython-312.pyc", "path_type": "hardlink", "sha256": "3e31a617134f07f26774b188767c382d789a457495fa1ab850d75af67162d953", "sha256_in_prefix": "3e31a617134f07f26774b188767c382d789a457495fa1ab850d75af67162d953", "size_in_bytes": 2054}, {"_path": "Lib/site-packages/setuptools/__pycache__/modified.cpython-312.pyc", "path_type": "hardlink", "sha256": "a3ebde985027a6a8c246d9ffc4fada8eccdd38bf64d1864dc1b8784e2cd7478c", "sha256_in_prefix": "a3ebde985027a6a8c246d9ffc4fada8eccdd38bf64d1864dc1b8784e2cd7478c", "size_in_bytes": 453}, {"_path": "Lib/site-packages/setuptools/__pycache__/monkey.cpython-312.pyc", "path_type": "hardlink", "sha256": "a100bb0fd197765c7baa6c9b78f425884dddc8dcc73b1dbcd3da1a64a33cee43", "sha256_in_prefix": "a100bb0fd197765c7baa6c9b78f425884dddc8dcc73b1dbcd3da1a64a33cee43", "size_in_bytes": 5136}, {"_path": "Lib/site-packages/setuptools/__pycache__/msvc.cpython-312.pyc", "path_type": "hardlink", "sha256": "d7ddb6ce913807109ef1315819c2aaf5112e3a138e01b6e225661260e574c34a", "sha256_in_prefix": "d7ddb6ce913807109ef1315819c2aaf5112e3a138e01b6e225661260e574c34a", "size_in_bytes": 58656}, {"_path": "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-312.pyc", "path_type": "hardlink", "sha256": "95bf37be79ba5121af0909c8cb86e89a2f42973f69837cab66b1095daf0d2736", "sha256_in_prefix": "95bf37be79ba5121af0909c8cb86e89a2f42973f69837cab66b1095daf0d2736", "size_in_bytes": 5255}, {"_path": "Lib/site-packages/setuptools/__pycache__/package_index.cpython-312.pyc", "path_type": "hardlink", "sha256": "2bb4e19bed2f7f3c1e63886caea8fecd97edafb82a996528be88eaace5b9e625", "sha256_in_prefix": "2bb4e19bed2f7f3c1e63886caea8fecd97edafb82a996528be88eaace5b9e625", "size_in_bytes": 55580}, {"_path": "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-312.pyc", "path_type": "hardlink", "sha256": "b9a04e6856f81a121b89bb325d7c0a3c91245f1b811adc8b5df1a039975599aa", "sha256_in_prefix": "b9a04e6856f81a121b89bb325d7c0a3c91245f1b811adc8b5df1a039975599aa", "size_in_bytes": 23896}, {"_path": "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-312.pyc", "path_type": "hardlink", "sha256": "5c8f14a30e064c898bdaea33c5eeb1371bc77c82e2c630f0797dbd3d36e27a11", "sha256_in_prefix": "5c8f14a30e064c898bdaea33c5eeb1371bc77c82e2c630f0797dbd3d36e27a11", "size_in_bytes": 4263}, {"_path": "Lib/site-packages/setuptools/__pycache__/version.cpython-312.pyc", "path_type": "hardlink", "sha256": "113f105963033a3795ee1cb85586c3284314918d15cd25855409ed3f752ff74c", "sha256_in_prefix": "113f105963033a3795ee1cb85586c3284314918d15cd25855409ed3f752ff74c", "size_in_bytes": 376}, {"_path": "Lib/site-packages/setuptools/__pycache__/warnings.cpython-312.pyc", "path_type": "hardlink", "sha256": "70498468ba9215331ecc3d172a4b797082c0cd7052a8e47df3a6aca163d2c45c", "sha256_in_prefix": "70498468ba9215331ecc3d172a4b797082c0cd7052a8e47df3a6aca163d2c45c", "size_in_bytes": 5095}, {"_path": "Lib/site-packages/setuptools/__pycache__/wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "037816792075fb9183c3c278662661f96f83999d25b87f7786f261da3eb5306e", "sha256_in_prefix": "037816792075fb9183c3c278662661f96f83999d25b87f7786f261da3eb5306e", "size_in_bytes": 13275}, {"_path": "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-312.pyc", "path_type": "hardlink", "sha256": "9be43e02f3f86b5e953eda07f1a237895e76e7250c1de655a5494f759caa8ef7", "sha256_in_prefix": "9be43e02f3f86b5e953eda07f1a237895e76e7250c1de655a5494f759caa8ef7", "size_in_bytes": 1413}, {"_path": "Lib/site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "sha256_in_prefix": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "size_in_bytes": 11978}, {"_path": "Lib/site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "1d7f15c12aa6832906ba1db2a7cba9ccddf3443bccfc87e49594c9f8726abb39", "sha256_in_prefix": "1d7f15c12aa6832906ba1db2a7cba9ccddf3443bccfc87e49594c9f8726abb39", "size_in_bytes": 484}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-312.pyc", "path_type": "hardlink", "sha256": "2b6013fc3ce6ef44988a3671a1b7d607b56ec49536608c3bae52ed3cc3dabe3b", "sha256_in_prefix": "2b6013fc3ce6ef44988a3671a1b7d607b56ec49536608c3bae52ed3cc3dabe3b", "size_in_bytes": 232}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-312.pyc", "path_type": "hardlink", "sha256": "b15327a206eeb71701c9dee99b0b236ebcf8d2da75e73bad4cf53543bf5322f1", "sha256_in_prefix": "b15327a206eeb71701c9dee99b0b236ebcf8d2da75e73bad4cf53543bf5322f1", "size_in_bytes": 521}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-312.pyc", "path_type": "hardlink", "sha256": "541b5456b53264e2beabae88683592df447a74f113d646605e14fdc65e022425", "sha256_in_prefix": "541b5456b53264e2beabae88683592df447a74f113d646605e14fdc65e022425", "size_in_bytes": 4746}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-312.pyc", "path_type": "hardlink", "sha256": "0fa2509606d91d083e98531a15fd53333074784a1dce13e2008d7a701bb6450f", "sha256_in_prefix": "0fa2509606d91d083e98531a15fd53333074784a1dce13e2008d7a701bb6450f", "size_in_bytes": 679}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "787220eeee39659635bcba5b79c0d0269986218e3a155e8e02754fddfe0323a8", "sha256_in_prefix": "787220eeee39659635bcba5b79c0d0269986218e3a155e8e02754fddfe0323a8", "size_in_bytes": 10772}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-312.pyc", "path_type": "hardlink", "sha256": "00ac1cda107dda9af7b0ad266d9dac56b6107a8acb1f01541433acfcfdcd1a27", "sha256_in_prefix": "00ac1cda107dda9af7b0ad266d9dac56b6107a8acb1f01541433acfcfdcd1a27", "size_in_bytes": 648}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-312.pyc", "path_type": "hardlink", "sha256": "7e43854fc358567a61bfe020cc8e1ba2ccf4735e500596a20aef4c21e3ba57e6", "sha256_in_prefix": "7e43854fc358567a61bfe020cc8e1ba2ccf4735e500596a20aef4c21e3ba57e6", "size_in_bytes": 22537}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-312.pyc", "path_type": "hardlink", "sha256": "b322be0d42807ca8d3a097f973fcda708fb3d9e21217bd498673ee37fbcf3fbd", "sha256_in_prefix": "b322be0d42807ca8d3a097f973fcda708fb3d9e21217bd498673ee37fbcf3fbd", "size_in_bytes": 9064}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-312.pyc", "path_type": "hardlink", "sha256": "0e56f3e780a512d8d1753d4dd39e7cb806d41406964bd0b60036a02a316192c5", "sha256_in_prefix": "0e56f3e780a512d8d1753d4dd39e7cb806d41406964bd0b60036a02a316192c5", "size_in_bytes": 612}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-312.pyc", "path_type": "hardlink", "sha256": "a01393eb4be99b37f4d78cec3056cffb362d4e6c6967f6acdcfd2dc12d30aa01", "sha256_in_prefix": "a01393eb4be99b37f4d78cec3056cffb362d4e6c6967f6acdcfd2dc12d30aa01", "size_in_bytes": 275}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "daf4b0019316f48e2bcf3a51a630a059772526c1dac87ea6b1c4fa5d5d0f19ca", "sha256_in_prefix": "daf4b0019316f48e2bcf3a51a630a059772526c1dac87ea6b1c4fa5d5d0f19ca", "size_in_bytes": 669}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "83f76885b829dea5a8c16eb543357cb4476bc899910594f34070ab39b74ba789", "sha256_in_prefix": "83f76885b829dea5a8c16eb543357cb4476bc899910594f34070ab39b74ba789", "size_in_bytes": 10302}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-312.pyc", "path_type": "hardlink", "sha256": "cfd912394e17baadb9d17f972939ec676902f02b493ddfe4e78cf5142f5fef51", "sha256_in_prefix": "cfd912394e17baadb9d17f972939ec676902f02b493ddfe4e78cf5142f5fef51", "size_in_bytes": 56103}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-312.pyc", "path_type": "hardlink", "sha256": "d996982475b7b3019aa0f7022f8f52133e0d2cbca2ab22905700ba26a427e07d", "sha256_in_prefix": "d996982475b7b3019aa0f7022f8f52133e0d2cbca2ab22905700ba26a427e07d", "size_in_bytes": 4484}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-312.pyc", "path_type": "hardlink", "sha256": "5a13132fcdffb6966bf42ed6727086d14350ba92c3d7a814aaa6bce527bce8b2", "sha256_in_prefix": "5a13132fcdffb6966bf42ed6727086d14350ba92c3d7a814aaa6bce527bce8b2", "size_in_bytes": 10201}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-312.pyc", "path_type": "hardlink", "sha256": "de64199b5363fb65d18e8f64a34bec6fbe379f610cb9589c036a04b5bc45db75", "sha256_in_prefix": "de64199b5363fb65d18e8f64a34bec6fbe379f610cb9589c036a04b5bc45db75", "size_in_bytes": 15712}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "ea608b3290fa482395f8827867de452e858acb1bf62c886cd2250fb1a92b1c90", "sha256_in_prefix": "ea608b3290fa482395f8827867de452e858acb1bf62c886cd2250fb1a92b1c90", "size_in_bytes": 9440}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-312.pyc", "path_type": "hardlink", "sha256": "9f1c282ac0e3e360a154b5e8d14226799aaae4cebd93ef9731ce6aac8963ab46", "sha256_in_prefix": "9f1c282ac0e3e360a154b5e8d14226799aaae4cebd93ef9731ce6aac8963ab46", "size_in_bytes": 17839}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-312.pyc", "path_type": "hardlink", "sha256": "b81f18ea6f135d73246af32d8064a0a68143c5f655384025628aa3d2139d69f7", "sha256_in_prefix": "b81f18ea6f135d73246af32d8064a0a68143c5f655384025628aa3d2139d69f7", "size_in_bytes": 2570}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-312.pyc", "path_type": "hardlink", "sha256": "e970314f4f809b97078b10fdfbbec1f8265705e494406e63b24263d9ecb9e959", "sha256_in_prefix": "e970314f4f809b97078b10fdfbbec1f8265705e494406e63b24263d9ecb9e959", "size_in_bytes": 5596}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-312.pyc", "path_type": "hardlink", "sha256": "bf11b4eb90adc9f95066e13d2126b7ee71ae397f6d3c4ad37ee645a9e5efaab0", "sha256_in_prefix": "bf11b4eb90adc9f95066e13d2126b7ee71ae397f6d3c4ad37ee645a9e5efaab0", "size_in_bytes": 23060}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-312.pyc", "path_type": "hardlink", "sha256": "0c32bf08a564ebff6933a765aef4fcea47fcd3dd8ab6c611a0c21daa20a8e000", "sha256_in_prefix": "0c32bf08a564ebff6933a765aef4fcea47fcd3dd8ab6c611a0c21daa20a8e000", "size_in_bytes": 10782}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-312.pyc", "path_type": "hardlink", "sha256": "71969d6d2c4f95b24ae46c514acd7157a86e4933b8e1541aa45235480e4f30b9", "sha256_in_prefix": "71969d6d2c4f95b24ae46c514acd7157a86e4933b8e1541aa45235480e4f30b9", "size_in_bytes": 372}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-312.pyc", "path_type": "hardlink", "sha256": "0411245fd07844cfcb302abe64ec7bd8925ed0f91dc06b661f7460f41de25171", "sha256_in_prefix": "0411245fd07844cfcb302abe64ec7bd8925ed0f91dc06b661f7460f41de25171", "size_in_bytes": 19429}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-312.pyc", "path_type": "hardlink", "sha256": "85cd8a4c7d7775e26e1d696f426ea4dcc7bc30141c97acb24388d782e1d21ca4", "sha256_in_prefix": "85cd8a4c7d7775e26e1d696f426ea4dcc7bc30141c97acb24388d782e1d21ca4", "size_in_bytes": 10567}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-312.pyc", "path_type": "hardlink", "sha256": "9849028e0a82f65f74c321d388f1e463c8f14093b777bd903c7da88c81e865c0", "sha256_in_prefix": "9849028e0a82f65f74c321d388f1e463c8f14093b777bd903c7da88c81e865c0", "size_in_bytes": 6843}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-312.pyc", "path_type": "hardlink", "sha256": "7b496e7a7cef2bafaa79245a01a03b62727423725b4f48780c7b39fcc1a4ad32", "sha256_in_prefix": "7b496e7a7cef2bafaa79245a01a03b62727423725b4f48780c7b39fcc1a4ad32", "size_in_bytes": 251}, {"_path": "Lib/site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "Lib/site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "sha256_in_prefix": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "size_in_bytes": 3211}, {"_path": "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "sha256_in_prefix": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "sha256_in_prefix": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "size_in_bytes": 8884}, {"_path": "Lib/site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "sha256_in_prefix": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "size_in_bytes": 524}, {"_path": "Lib/site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "sha256_in_prefix": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "size_in_bytes": 22186}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "f2e0416983b7b604818dec97cdffee13e64cb274d9db634b62c53221c9346ed7", "sha256_in_prefix": "f2e0416983b7b604818dec97cdffee13e64cb274d9db634b62c53221c9346ed7", "size_in_bytes": 467}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-312.pyc", "path_type": "hardlink", "sha256": "9c4f1335e500ddbc5a41576cbf3f8ff4026220a4e44ba0410b19a98919a39cbd", "sha256_in_prefix": "9c4f1335e500ddbc5a41576cbf3f8ff4026220a4e44ba0410b19a98919a39cbd", "size_in_bytes": 2571}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-312.pyc", "path_type": "hardlink", "sha256": "0a627c33b108f164529a3c45362d62e06a8d467fd6a27a162d41aa18dbb33643", "sha256_in_prefix": "0a627c33b108f164529a3c45362d62e06a8d467fd6a27a162d41aa18dbb33643", "size_in_bytes": 6409}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-312.pyc", "path_type": "hardlink", "sha256": "c5502b32c1619895be3bba9b3835c065a5eeacda8819584929743a270a5fa41c", "sha256_in_prefix": "c5502b32c1619895be3bba9b3835c065a5eeacda8819584929743a270a5fa41c", "size_in_bytes": 5540}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-312.pyc", "path_type": "hardlink", "sha256": "70141e64d57848339182f985125f47527dd69b7cce3953791ef45ab57a935e84", "sha256_in_prefix": "70141e64d57848339182f985125f47527dd69b7cce3953791ef45ab57a935e84", "size_in_bytes": 21577}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-312.pyc", "path_type": "hardlink", "sha256": "04ab4cb5c45ca01080bf469f001ca0d5fd8490eec5d66ce647eb7f0222bb2107", "sha256_in_prefix": "04ab4cb5c45ca01080bf469f001ca0d5fd8490eec5d66ce647eb7f0222bb2107", "size_in_bytes": 6228}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-312.pyc", "path_type": "hardlink", "sha256": "41d87e038bd7d7a861f3b44b004aa116fee1394bff40c5b59ec032ba66887f99", "sha256_in_prefix": "41d87e038bd7d7a861f3b44b004aa116fee1394bff40c5b59ec032ba66887f99", "size_in_bytes": 7543}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-312.pyc", "path_type": "hardlink", "sha256": "136ad1f1f52b6bda92d03b9efa59e7bb953f85ca340b17ee3f8d8a7e9b43cf45", "sha256_in_prefix": "136ad1f1f52b6bda92d03b9efa59e7bb953f85ca340b17ee3f8d8a7e9b43cf45", "size_in_bytes": 30099}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-312.pyc", "path_type": "hardlink", "sha256": "a11586d0ac32855c53e3b6387d9c70505af9a69ddef3e8e310d4ee60195fa286", "sha256_in_prefix": "a11586d0ac32855c53e3b6387d9c70505af9a69ddef3e8e310d4ee60195fa286", "size_in_bytes": 16189}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-312.pyc", "path_type": "hardlink", "sha256": "1fe16a13fff639563e1fe74a1553ff3935ffc6704149638ca677b58985a1f80a", "sha256_in_prefix": "1fe16a13fff639563e1fe74a1553ff3935ffc6704149638ca677b58985a1f80a", "size_in_bytes": 6706}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-312.pyc", "path_type": "hardlink", "sha256": "7012e7bf583eba61e2e16ad8c5bcad57c8005094e620063accb371d6bbcc2d4e", "sha256_in_prefix": "7012e7bf583eba61e2e16ad8c5bcad57c8005094e620063accb371d6bbcc2d4e", "size_in_bytes": 7118}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-312.pyc", "path_type": "hardlink", "sha256": "9f03d5996d5ea431556d54e8424d64fb5f7dc7694749ebc7741300e69b621eaa", "sha256_in_prefix": "9f03d5996d5ea431556d54e8424d64fb5f7dc7694749ebc7741300e69b621eaa", "size_in_bytes": 3088}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-312.pyc", "path_type": "hardlink", "sha256": "b5c4a22fe02de87d67b5a14dd8f5b2e53b7936e12617258b369771945ed4cc7a", "sha256_in_prefix": "b5c4a22fe02de87d67b5a14dd8f5b2e53b7936e12617258b369771945ed4cc7a", "size_in_bytes": 15113}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-312.pyc", "path_type": "hardlink", "sha256": "5394c58f24e78351818697cc51272632234bd3696977703ec182eb51f2dea450", "sha256_in_prefix": "5394c58f24e78351818697cc51272632234bd3696977703ec182eb51f2dea450", "size_in_bytes": 26909}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-312.pyc", "path_type": "hardlink", "sha256": "ffac0d09f39bd033d6436aa557959c321098ff3862156ce6ce40f902c03b4245", "sha256_in_prefix": "ffac0d09f39bd033d6436aa557959c321098ff3862156ce6ce40f902c03b4245", "size_in_bytes": 4300}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-312.pyc", "path_type": "hardlink", "sha256": "8004f26dfb9f5b253471b0da7a73b87041d0c3fc0285f12ff50304ef893b1b6a", "sha256_in_prefix": "8004f26dfb9f5b253471b0da7a73b87041d0c3fc0285f12ff50304ef893b1b6a", "size_in_bytes": 5114}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-312.pyc", "path_type": "hardlink", "sha256": "054c27d9548c745268aeb62a5e6dda2d7fb8eff6b11b634daab529f167229900", "sha256_in_prefix": "054c27d9548c745268aeb62a5e6dda2d7fb8eff6b11b634daab529f167229900", "size_in_bytes": 2374}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-312.pyc", "path_type": "hardlink", "sha256": "fb6e59563a27c76c271d73b07d71aa1096ed87b9ef646c15df86b65e613b9fa6", "sha256_in_prefix": "fb6e59563a27c76c271d73b07d71aa1096ed87b9ef646c15df86b65e613b9fa6", "size_in_bytes": 8273}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-312.pyc", "path_type": "hardlink", "sha256": "76564de4db36fbf69ce11c4214aba2774eff0d9b46745102f03c81c31188cf95", "sha256_in_prefix": "76564de4db36fbf69ce11c4214aba2774eff0d9b46745102f03c81c31188cf95", "size_in_bytes": 3033}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-312.pyc", "path_type": "hardlink", "sha256": "8cddc4aa058829b1821bb501e95f9229a904d018451db5c5420f773bcdc953a0", "sha256_in_prefix": "8cddc4aa058829b1821bb501e95f9229a904d018451db5c5420f773bcdc953a0", "size_in_bytes": 22437}, {"_path": "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "sha256_in_prefix": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "size_in_bytes": 5854}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "sha256_in_prefix": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "size_in_bytes": 4631}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "sha256_in_prefix": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "size_in_bytes": 21785}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "sha256_in_prefix": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "size_in_bytes": 5923}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "sha256_in_prefix": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "size_in_bytes": 7777}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "sha256_in_prefix": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "size_in_bytes": 32710}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "sha256_in_prefix": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "size_in_bytes": 16696}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "sha256_in_prefix": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "size_in_bytes": 5118}, {"_path": "Lib/site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "sha256_in_prefix": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "size_in_bytes": 4946}, {"_path": "Lib/site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "sha256_in_prefix": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "size_in_bytes": 2644}, {"_path": "Lib/site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "sha256_in_prefix": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "size_in_bytes": 12818}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "sha256_in_prefix": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "size_in_bytes": 30072}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "sha256_in_prefix": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "size_in_bytes": 2875}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "sha256_in_prefix": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "size_in_bytes": 2868}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "sha256_in_prefix": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "size_in_bytes": 1272}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "sha256_in_prefix": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "size_in_bytes": 8588}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "sha256_in_prefix": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "size_in_bytes": 2002}, {"_path": "Lib/site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "sha256_in_prefix": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "size_in_bytes": 19151}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "sha256_in_prefix": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "size_in_bytes": 522}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "56a935f09db12a2dbae406ea035375e3e84a159defe95ce28f89042693cc6d89", "sha256_in_prefix": "56a935f09db12a2dbae406ea035375e3e84a159defe95ce28f89042693cc6d89", "size_in_bytes": 1266}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-312.pyc", "path_type": "hardlink", "sha256": "3200bdd67071d2cbeca6c431d465ceb9c6577f6859f48262e59036c01e00c8a0", "sha256_in_prefix": "3200bdd67071d2cbeca6c431d465ceb9c6577f6859f48262e59036c01e00c8a0", "size_in_bytes": 242}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-312.pyc", "path_type": "hardlink", "sha256": "482bb0029d38410a7af721062e6f3ca3e77d1b7ad876d89108681c9697221ebd", "sha256_in_prefix": "482bb0029d38410a7af721062e6f3ca3e77d1b7ad876d89108681c9697221ebd", "size_in_bytes": 2639}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "path_type": "hardlink", "sha256": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "sha256_in_prefix": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-312.pyc", "path_type": "hardlink", "sha256": "b2e5e5f5c64efb536ab30ce72f3caca3051ac68d52b66600e9df5b4f3b54afd5", "sha256_in_prefix": "b2e5e5f5c64efb536ab30ce72f3caca3051ac68d52b66600e9df5b4f3b54afd5", "size_in_bytes": 52261}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-312.pyc", "path_type": "hardlink", "sha256": "71fac38b97094332cc3ec5426c309c5af96c0d57700a950322512088c57e3b7e", "sha256_in_prefix": "71fac38b97094332cc3ec5426c309c5af96c0d57700a950322512088c57e3b7e", "size_in_bytes": 11752}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-312.pyc", "path_type": "hardlink", "sha256": "c82234f3e7400bd5f001776838386dbfc91c759bdab2b97bbf70d8fad26da9b4", "sha256_in_prefix": "c82234f3e7400bd5f001776838386dbfc91c759bdab2b97bbf70d8fad26da9b4", "size_in_bytes": 1467}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-312.pyc", "path_type": "hardlink", "sha256": "6569af3e145b73083aa685c7860bec73199d9f15fc32225a6c4bd0050d680ee3", "sha256_in_prefix": "6569af3e145b73083aa685c7860bec73199d9f15fc32225a6c4bd0050d680ee3", "size_in_bytes": 25072}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-312.pyc", "path_type": "hardlink", "sha256": "68bca4389c7db085cec03257c762a4ce7ae7960eef2bc687cbb55fd4f12ecaf4", "sha256_in_prefix": "68bca4389c7db085cec03257c762a4ce7ae7960eef2bc687cbb55fd4f12ecaf4", "size_in_bytes": 15853}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-312.pyc", "path_type": "hardlink", "sha256": "7d9a309724b157fc00825f713cee16091bd2cae162f76d9945fadddb90edb4f5", "sha256_in_prefix": "7d9a309724b157fc00825f713cee16091bd2cae162f76d9945fadddb90edb4f5", "size_in_bytes": 6188}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "path_type": "hardlink", "sha256": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "sha256_in_prefix": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "size_in_bytes": 54876}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "path_type": "hardlink", "sha256": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "sha256_in_prefix": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "size_in_bytes": 11844}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "path_type": "hardlink", "sha256": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "sha256_in_prefix": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "size_in_bytes": 573}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "path_type": "hardlink", "sha256": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "sha256_in_prefix": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "size_in_bytes": 21802}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-312.pyc", "path_type": "hardlink", "sha256": "dc9533b872f89201cf9e490a85b53eef48baf2187fc2f36622849f95441ee90c", "sha256_in_prefix": "dc9533b872f89201cf9e490a85b53eef48baf2187fc2f36622849f95441ee90c", "size_in_bytes": 3977}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-312.pyc", "path_type": "hardlink", "sha256": "9d8e23b455f2ff2a24cddde0794eae806bb814d5152c19b36aa956b2a459ed57", "sha256_in_prefix": "9d8e23b455f2ff2a24cddde0794eae806bb814d5152c19b36aa956b2a459ed57", "size_in_bytes": 4516}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-312.pyc", "path_type": "hardlink", "sha256": "44cdee355141454e0a89bba61d930f1f4e35493c3753f04bce289aceb827c427", "sha256_in_prefix": "44cdee355141454e0a89bba61d930f1f4e35493c3753f04bce289aceb827c427", "size_in_bytes": 3827}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-312.pyc", "path_type": "hardlink", "sha256": "63468b5acdd63c98e3e583e752371f3bdf1e2b4d2e414988ea5e89c299baf841", "sha256_in_prefix": "63468b5acdd63c98e3e583e752371f3bdf1e2b4d2e414988ea5e89c299baf841", "size_in_bytes": 7409}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-312.pyc", "path_type": "hardlink", "sha256": "af097c865993345007d3d8256320e2c74aeaad9bac585f383ba3a49e006d13a8", "sha256_in_prefix": "af097c865993345007d3d8256320e2c74aeaad9bac585f383ba3a49e006d13a8", "size_in_bytes": 15788}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "path_type": "hardlink", "sha256": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "sha256_in_prefix": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "size_in_bytes": 2706}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "path_type": "hardlink", "sha256": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "sha256_in_prefix": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "size_in_bytes": 2701}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "path_type": "hardlink", "sha256": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "sha256_in_prefix": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "size_in_bytes": 1900}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "path_type": "hardlink", "sha256": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "sha256_in_prefix": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "size_in_bytes": 4151}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "path_type": "hardlink", "sha256": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "sha256_in_prefix": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "size_in_bytes": 11834}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "path_type": "hardlink", "sha256": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "sha256_in_prefix": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "size_in_bytes": 16502}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "path_type": "hardlink", "sha256": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "sha256_in_prefix": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "size_in_bytes": 6586}, {"_path": "Lib/site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "sha256_in_prefix": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "size_in_bytes": 9364}, {"_path": "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "sha256_in_prefix": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "size_in_bytes": 594}, {"_path": "Lib/site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "Lib/site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "Lib/site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "sha256_in_prefix": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "size_in_bytes": 7236}, {"_path": "Lib/site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "sha256_in_prefix": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "size_in_bytes": 55794}, {"_path": "Lib/site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "sha256_in_prefix": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "size_in_bytes": 3092}, {"_path": "Lib/site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "sha256_in_prefix": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "size_in_bytes": 11155}, {"_path": "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "sha256_in_prefix": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "size_in_bytes": 17895}, {"_path": "Lib/site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "sha256_in_prefix": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "size_in_bytes": 7978}, {"_path": "Lib/site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "sha256_in_prefix": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "size_in_bytes": 15337}, {"_path": "Lib/site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "Lib/site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "sha256_in_prefix": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "size_in_bytes": 4086}, {"_path": "Lib/site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "sha256_in_prefix": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "size_in_bytes": 19728}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "sha256_in_prefix": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "size_in_bytes": 1485}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "2745df3f21ac4401324be52108b76388ce3c70c18eea536f5c66d50ce37cda44", "sha256_in_prefix": "2745df3f21ac4401324be52108b76388ce3c70c18eea536f5c66d50ce37cda44", "size_in_bytes": 1863}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-312.pyc", "path_type": "hardlink", "sha256": "fb4b924f96348b02a856ae91a6ac56ccdebb66e4c4a2579f806e1d702ab60785", "sha256_in_prefix": "fb4b924f96348b02a856ae91a6ac56ccdebb66e4c4a2579f806e1d702ab60785", "size_in_bytes": 6357}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "d8fda1d82bc4eee435b32c0909253a588720c7c40ed11ea1ee9bf90eb47e0727", "sha256_in_prefix": "d8fda1d82bc4eee435b32c0909253a588720c7c40ed11ea1ee9bf90eb47e0727", "size_in_bytes": 20658}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-312.pyc", "path_type": "hardlink", "sha256": "c485676156d15b9c6963ebd11f85ec36ca0e2d39909e9aa42812b9db699d080e", "sha256_in_prefix": "c485676156d15b9c6963ebd11f85ec36ca0e2d39909e9aa42812b9db699d080e", "size_in_bytes": 1846}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-312.pyc", "path_type": "hardlink", "sha256": "3b31a6979aebdbdd5dcf6e4bd648a914bd09cf060f3d59d2e97689d3764911e6", "sha256_in_prefix": "3b31a6979aebdbdd5dcf6e4bd648a914bd09cf060f3d59d2e97689d3764911e6", "size_in_bytes": 3800}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-312.pyc", "path_type": "hardlink", "sha256": "de2f97327e736fd131784ec5c62288be8837cb6e7e128b35c87e294bd0559ca1", "sha256_in_prefix": "de2f97327e736fd131784ec5c62288be8837cb6e7e128b35c87e294bd0559ca1", "size_in_bytes": 5582}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-312.pyc", "path_type": "hardlink", "sha256": "c729d03c923643aac8e8cbafb6f77b7384f4b4984b07635958f2adc84333f245", "sha256_in_prefix": "c729d03c923643aac8e8cbafb6f77b7384f4b4984b07635958f2adc84333f245", "size_in_bytes": 2738}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-312.pyc", "path_type": "hardlink", "sha256": "623337812412b4bf9098acd27c6aed43606fee2f5d363174e2df521baf638a48", "sha256_in_prefix": "623337812412b4bf9098acd27c6aed43606fee2f5d363174e2df521baf638a48", "size_in_bytes": 6519}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-312.pyc", "path_type": "hardlink", "sha256": "cf1ad92bfa5e0a97150247d5b08078a789dea8c176d8b4a9ce3dcd58f55ce813", "sha256_in_prefix": "cf1ad92bfa5e0a97150247d5b08078a789dea8c176d8b4a9ce3dcd58f55ce813", "size_in_bytes": 29176}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-312.pyc", "path_type": "hardlink", "sha256": "b7cbd1add0c5013167cdc3bae4d1f0512a275aac9efcda3720228d35267199a2", "sha256_in_prefix": "b7cbd1add0c5013167cdc3bae4d1f0512a275aac9efcda3720228d35267199a2", "size_in_bytes": 9809}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-312.pyc", "path_type": "hardlink", "sha256": "f32acb98897a51e475d009f449c4ac821e9610f7b63e0cc16eb192bc4c8000ed", "sha256_in_prefix": "f32acb98897a51e475d009f449c4ac821e9610f7b63e0cc16eb192bc4c8000ed", "size_in_bytes": 4651}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-312.pyc", "path_type": "hardlink", "sha256": "3c783f748f4c8d566c62c25f31773554d41e3e88370b89ea477719d3753f387d", "sha256_in_prefix": "3c783f748f4c8d566c62c25f31773554d41e3e88370b89ea477719d3753f387d", "size_in_bytes": 7053}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-312.pyc", "path_type": "hardlink", "sha256": "59086a8da762d3e2561d61df6b1dabd3f7b3594f2837501a99525c2abb43e58a", "sha256_in_prefix": "59086a8da762d3e2561d61df6b1dabd3f7b3594f2837501a99525c2abb43e58a", "size_in_bytes": 1866}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-312.pyc", "path_type": "hardlink", "sha256": "55c0c0fa7907c6c13d397da53516c7aae049b6eeee2e96dcd8c5f03840362e10", "sha256_in_prefix": "55c0c0fa7907c6c13d397da53516c7aae049b6eeee2e96dcd8c5f03840362e10", "size_in_bytes": 6241}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-312.pyc", "path_type": "hardlink", "sha256": "88733b6811b3017460999e8e072855ed41233dbe6c11d82de5f7d02960ba57b8", "sha256_in_prefix": "88733b6811b3017460999e8e072855ed41233dbe6c11d82de5f7d02960ba57b8", "size_in_bytes": 5065}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-312.pyc", "path_type": "hardlink", "sha256": "9990d2a281124399ea8f522d3be5194e46e4609db186ddac51ebfb5818713894", "sha256_in_prefix": "9990d2a281124399ea8f522d3be5194e46e4609db186ddac51ebfb5818713894", "size_in_bytes": 6138}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "e3468ce623566fb5979cd681406ff75aaa10f97ac6bf07ea03cf8d45aaab676f", "sha256_in_prefix": "e3468ce623566fb5979cd681406ff75aaa10f97ac6bf07ea03cf8d45aaab676f", "size_in_bytes": 8510}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-312.pyc", "path_type": "hardlink", "sha256": "5c2f16a1154a6046edbfac515701ec522646fff4955690b03fef2000a274f7f4", "sha256_in_prefix": "5c2f16a1154a6046edbfac515701ec522646fff4955690b03fef2000a274f7f4", "size_in_bytes": 26748}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-312.pyc", "path_type": "hardlink", "sha256": "afc2e3f719af01eb5b1bfbb3728b7f9e346328ee2feb0036fc24da643d72dc7b", "sha256_in_prefix": "afc2e3f719af01eb5b1bfbb3728b7f9e346328ee2feb0036fc24da643d72dc7b", "size_in_bytes": 4111}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "2cbddb950a45f164f6f2cd2b433cb7d7d783e887e901ec68e9534e1d5b681259", "sha256_in_prefix": "2cbddb950a45f164f6f2cd2b433cb7d7d783e887e901ec68e9534e1d5b681259", "size_in_bytes": 6837}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-312.pyc", "path_type": "hardlink", "sha256": "05f6584ffe5111f0ce9efef491cd093d2600e163b1f08829a1f65b0234e426f4", "sha256_in_prefix": "05f6584ffe5111f0ce9efef491cd093d2600e163b1f08829a1f65b0234e426f4", "size_in_bytes": 14256}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-312.pyc", "path_type": "hardlink", "sha256": "4311fd03609f520aa37fc8e22b018d76307aa80c3b58ae4b35431a9e7330ac30", "sha256_in_prefix": "4311fd03609f520aa37fc8e22b018d76307aa80c3b58ae4b35431a9e7330ac30", "size_in_bytes": 14005}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-312.pyc", "path_type": "hardlink", "sha256": "d1b5c41b48b3a6f0cc2bde5216f91b58162353cf5bf8dd78cc4b7767af4e79d5", "sha256_in_prefix": "d1b5c41b48b3a6f0cc2bde5216f91b58162353cf5bf8dd78cc4b7767af4e79d5", "size_in_bytes": 4751}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-312.pyc", "path_type": "hardlink", "sha256": "6a5800e4d04125a52d5f440e8aa85312629bc87bfc598221995c18cf56627c50", "sha256_in_prefix": "6a5800e4d04125a52d5f440e8aa85312629bc87bfc598221995c18cf56627c50", "size_in_bytes": 1840}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-312.pyc", "path_type": "hardlink", "sha256": "edc37e873688502019bf6f6b2db7892d9c4b17d1fcc3df23492b65b75e004752", "sha256_in_prefix": "edc37e873688502019bf6f6b2db7892d9c4b17d1fcc3df23492b65b75e004752", "size_in_bytes": 6061}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-312.pyc", "path_type": "hardlink", "sha256": "db617bd1bf6a814640c75db2740f3bebed2c62e5c244f9bf8c99abee0b269ed2", "sha256_in_prefix": "db617bd1bf6a814640c75db2740f3bebed2c62e5c244f9bf8c99abee0b269ed2", "size_in_bytes": 2655}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-312.pyc", "path_type": "hardlink", "sha256": "ca25e78c34bc75376c3d588a0de34ba40ee7ddc981852a7dd795a8f611b7b467", "sha256_in_prefix": "ca25e78c34bc75376c3d588a0de34ba40ee7ddc981852a7dd795a8f611b7b467", "size_in_bytes": 912}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-312.pyc", "path_type": "hardlink", "sha256": "77f80d9e46fdecebc9f65bb862021e7b1d6782579b7600a6b1927dd64907873b", "sha256_in_prefix": "77f80d9e46fdecebc9f65bb862021e7b1d6782579b7600a6b1927dd64907873b", "size_in_bytes": 7132}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-312.pyc", "path_type": "hardlink", "sha256": "ede4746eca657eb4762142c76061ff5ba0e86c552c629f83b7be0bf94cadd4c5", "sha256_in_prefix": "ede4746eca657eb4762142c76061ff5ba0e86c552c629f83b7be0bf94cadd4c5", "size_in_bytes": 20520}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-312.pyc", "path_type": "hardlink", "sha256": "4a1128b0d7086ca0365a1f024b33d97f116c53f5c6d02818b8bb0f94b2fde72d", "sha256_in_prefix": "4a1128b0d7086ca0365a1f024b33d97f116c53f5c6d02818b8bb0f94b2fde72d", "size_in_bytes": 6628}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-312.pyc", "path_type": "hardlink", "sha256": "3833caddf85eac34ba237b24e39a497f985423c2c6f8307eb92a145093133d98", "sha256_in_prefix": "3833caddf85eac34ba237b24e39a497f985423c2c6f8307eb92a145093133d98", "size_in_bytes": 18085}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-312.pyc", "path_type": "hardlink", "sha256": "e0010a208068d45b569d48636b8df5129e4da3cc9336537bcfc78ceee80461dd", "sha256_in_prefix": "e0010a208068d45b569d48636b8df5129e4da3cc9336537bcfc78ceee80461dd", "size_in_bytes": 3291}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "d7ef823b54dc81ba9ea66f293d9c1ea030aaf82c44f2f5b891ef4b9afb25a48a", "sha256_in_prefix": "d7ef823b54dc81ba9ea66f293d9c1ea030aaf82c44f2f5b891ef4b9afb25a48a", "size_in_bytes": 13298}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-312.pyc", "path_type": "hardlink", "sha256": "480155815ef3d3d2d0833a0a25926aff75787aa5ca052323f168d5877b82780e", "sha256_in_prefix": "480155815ef3d3d2d0833a0a25926aff75787aa5ca052323f168d5877b82780e", "size_in_bytes": 3786}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-312.pyc", "path_type": "hardlink", "sha256": "a6b7d619056ba4a58ba69e813923c61df44e5da58c28357789558ac88c548190", "sha256_in_prefix": "a6b7d619056ba4a58ba69e813923c61df44e5da58c28357789558ac88c548190", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-312.pyc", "path_type": "hardlink", "sha256": "28ca209f4f0272012f652581cf7a83949c3416dbf883e68bcc03cd2e8cf882b9", "sha256_in_prefix": "28ca209f4f0272012f652581cf7a83949c3416dbf883e68bcc03cd2e8cf882b9", "size_in_bytes": 745}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "049e42ba38cc3ce0565ee3a4d4bb2d29023afac335caffcf220989b74692b6be", "sha256_in_prefix": "049e42ba38cc3ce0565ee3a4d4bb2d29023afac335caffcf220989b74692b6be", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-312.pyc", "path_type": "hardlink", "sha256": "14418e09b8c720c7587f694b400d0f111586f566f345e2953951aaf7381f61e8", "sha256_in_prefix": "14418e09b8c720c7587f694b400d0f111586f566f345e2953951aaf7381f61e8", "size_in_bytes": 645}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "path_type": "hardlink", "sha256": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "sha256_in_prefix": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "size_in_bytes": 1026}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "sha256_in_prefix": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "size_in_bytes": 3932}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "sha256_in_prefix": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "size_in_bytes": 1742}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "sha256_in_prefix": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "size_in_bytes": 22545}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "sha256_in_prefix": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "size_in_bytes": 4500}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "sha256_in_prefix": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "size_in_bytes": 18793}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "sha256_in_prefix": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "size_in_bytes": 3670}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "sha256_in_prefix": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "size_in_bytes": 3522}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "sha256_in_prefix": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "size_in_bytes": 10766}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "sha256_in_prefix": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "size_in_bytes": 15062}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "sha256_in_prefix": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "size_in_bytes": 4803}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "sha256_in_prefix": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "size_in_bytes": 11986}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "sha256_in_prefix": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "size_in_bytes": 2750}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "sha256_in_prefix": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "size_in_bytes": 12101}, {"_path": "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "sha256_in_prefix": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "size_in_bytes": 212}, {"_path": "Lib/site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "sha256_in_prefix": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "size_in_bytes": 18094}, {"_path": "Lib/site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "sha256_in_prefix": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "size_in_bytes": 12619}, {"_path": "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "sha256_in_prefix": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "size_in_bytes": 58}, {"_path": "Lib/site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "Lib/site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "sha256_in_prefix": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "size_in_bytes": 2435}, {"_path": "Lib/site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "sha256_in_prefix": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "size_in_bytes": 223}, {"_path": "Lib/site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "Lib/site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "sha256_in_prefix": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "size_in_bytes": 5824}, {"_path": "Lib/site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "sha256_in_prefix": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "size_in_bytes": 2685}, {"_path": "Lib/site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "sha256_in_prefix": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_shutil.py", "path_type": "hardlink", "sha256": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "sha256_in_prefix": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "size_in_bytes": 1496}, {"_path": "Lib/site-packages/setuptools/_static.py", "path_type": "hardlink", "sha256": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "sha256_in_prefix": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "size_in_bytes": 4855}, {"_path": "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-312.pyc", "path_type": "hardlink", "sha256": "5d03398d869627711e8446ed33d4e9026b0f8ca02a3c2492ca9c8c7f3a29a21e", "sha256_in_prefix": "5d03398d869627711e8446ed33d4e9026b0f8ca02a3c2492ca9c8c7f3a29a21e", "size_in_bytes": 139365}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "a0f06a3e86819a9e1c4be096defca64a66a9f619e03c1c24e6006ee87490e1b2", "sha256_in_prefix": "a0f06a3e86819a9e1c4be096defca64a66a9f619e03c1c24e6006ee87490e1b2", "size_in_bytes": 387}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-312.pyc", "path_type": "hardlink", "sha256": "48006bef1f269234a42955e9676fc09348fa75c0a567abdd05480c5957c627c3", "sha256_in_prefix": "48006bef1f269234a42955e9676fc09348fa75c0a567abdd05480c5957c627c3", "size_in_bytes": 4966}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-312.pyc", "path_type": "hardlink", "sha256": "fc5c27e8a3dcba2b863905ca8350c07382b3fb21dc0ef50582282965c7a68335", "sha256_in_prefix": "fc5c27e8a3dcba2b863905ca8350c07382b3fb21dc0ef50582282965c7a68335", "size_in_bytes": 1249}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-312.pyc", "path_type": "hardlink", "sha256": "40466f372b52a8da1c709d13a759e99c9866e898917219cf136c08f375cc883f", "sha256_in_prefix": "40466f372b52a8da1c709d13a759e99c9866e898917219cf136c08f375cc883f", "size_in_bytes": 1819}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-312.pyc", "path_type": "hardlink", "sha256": "75bcaa63a91c46f781de97fc809ad40f0386af10f893dceafa4b3e6a2ca3a2af", "sha256_in_prefix": "75bcaa63a91c46f781de97fc809ad40f0386af10f893dceafa4b3e6a2ca3a2af", "size_in_bytes": 10987}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-312.pyc", "path_type": "hardlink", "sha256": "e69228be42f5cbe2f92b1b149e6b958e19b78b301f88e9f4cd62a1f5f882421f", "sha256_in_prefix": "e69228be42f5cbe2f92b1b149e6b958e19b78b301f88e9f4cd62a1f5f882421f", "size_in_bytes": 403}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "5a1b10a1264a395bb938a80b974c4167ceea3bf551a2ebaf562883ff176d2cd7", "sha256_in_prefix": "5a1b10a1264a395bb938a80b974c4167ceea3bf551a2ebaf562883ff176d2cd7", "size_in_bytes": 277}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "5cd37a4280d5159d4a25d90e89102a3c157aad7bbc4829c6a2bbc3cd33f90224", "sha256_in_prefix": "5cd37a4280d5159d4a25d90e89102a3c157aad7bbc4829c6a2bbc3cd33f90224", "size_in_bytes": 121057}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-312.pyc", "path_type": "hardlink", "sha256": "5b747e42bf1b84acf810c7f7b323e996afc2f69e70382146abbd8c32662cbddb", "sha256_in_prefix": "5b747e42bf1b84acf810c7f7b323e996afc2f69e70382146abbd8c32662cbddb", "size_in_bytes": 261}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "b0c2ffe73c061ba285d9a2673ee359fc5ea55934db1c3db75a76db3b969eb47b", "sha256_in_prefix": "b0c2ffe73c061ba285d9a2673ee359fc5ea55934db1c3db75a76db3b969eb47b", "size_in_bytes": 170}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-312.pyc", "path_type": "hardlink", "sha256": "7dcd778bf64c35d27c50ffc495d781808e32f9547c476076ff560b7ce505e059", "sha256_in_prefix": "7dcd778bf64c35d27c50ffc495d781808e32f9547c476076ff560b7ce505e059", "size_in_bytes": 1016}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "b20c7bca292c8f6cb5d3ff5bb625e6e1112f46dd844056770615b8c1453d50cf", "sha256_in_prefix": "b20c7bca292c8f6cb5d3ff5bb625e6e1112f46dd844056770615b8c1453d50cf", "size_in_bytes": 53149}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-312.pyc", "path_type": "hardlink", "sha256": "2fe3864b12ba1f683514c4b081b4194b7ef88549dc092ea46100703b02858940", "sha256_in_prefix": "2fe3864b12ba1f683514c4b081b4194b7ef88549dc092ea46100703b02858940", "size_in_bytes": 3746}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-312.pyc", "path_type": "hardlink", "sha256": "a827dcf50d7d7c76e8544d55b5f357a6959cf32333bfa304cd830a149134868e", "sha256_in_prefix": "a827dcf50d7d7c76e8544d55b5f357a6959cf32333bfa304cd830a149134868e", "size_in_bytes": 1898}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-312.pyc", "path_type": "hardlink", "sha256": "94915db184e3ef3f94e54d7b7d5f48a0c877695ebd3f60ce3e30dc3955aa710d", "sha256_in_prefix": "94915db184e3ef3f94e54d7b7d5f48a0c877695ebd3f60ce3e30dc3955aa710d", "size_in_bytes": 2234}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-312.pyc", "path_type": "hardlink", "sha256": "e97ea31f7dbc144475d4ce4d0be54b89eeaf354db69e3d0481fa7938ed20f86c", "sha256_in_prefix": "e97ea31f7dbc144475d4ce4d0be54b89eeaf354db69e3d0481fa7938ed20f86c", "size_in_bytes": 3460}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-312.pyc", "path_type": "hardlink", "sha256": "f953e197b76fd8550f6a0faacc2f0d8bc48a28b4c1e29019a4ee02820e30d9a4", "sha256_in_prefix": "f953e197b76fd8550f6a0faacc2f0d8bc48a28b4c1e29019a4ee02820e30d9a4", "size_in_bytes": 2373}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-312.pyc", "path_type": "hardlink", "sha256": "ebe762799ee52cd211736fd4010797cb59c7cb220aa0cfb58802242929f24c74", "sha256_in_prefix": "ebe762799ee52cd211736fd4010797cb59c7cb220aa0cfb58802242929f24c74", "size_in_bytes": 3626}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-312.pyc", "path_type": "hardlink", "sha256": "b34df12aa541c73e2c99824e66ed333ab46153062655c27671b83a587fbe5356", "sha256_in_prefix": "b34df12aa541c73e2c99824e66ed333ab46153062655c27671b83a587fbe5356", "size_in_bytes": 3859}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-312.pyc", "path_type": "hardlink", "sha256": "0a50a263a3c6729510484fb4cebb6d8857416f0b760dcd2b3edfd0d03d7f5ee8", "sha256_in_prefix": "0a50a263a3c6729510484fb4cebb6d8857416f0b760dcd2b3edfd0d03d7f5ee8", "size_in_bytes": 1163}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "e640b1e559fc0bdf5ee053a6364bb44fa7ae1aa9ebc9b494271d1102a6b88411", "sha256_in_prefix": "e640b1e559fc0bdf5ee053a6364bb44fa7ae1aa9ebc9b494271d1102a6b88411", "size_in_bytes": 171}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-312.pyc", "path_type": "hardlink", "sha256": "aa37f2781d1d92ecda952d79f9a0f753677a894adf26362308acc0b14b462de4", "sha256_in_prefix": "aa37f2781d1d92ecda952d79f9a0f753677a894adf26362308acc0b14b462de4", "size_in_bytes": 1238}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-312.pyc", "path_type": "hardlink", "sha256": "a6e59ca1dccf4dff85a68d75bde59d26db697b20a5c9eb1abc10f735c3742e57", "sha256_in_prefix": "a6e59ca1dccf4dff85a68d75bde59d26db697b20a5c9eb1abc10f735c3742e57", "size_in_bytes": 1614}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "031d1cb61ff4c11c80f2c2d063008524528caf089efd3b4fd7c3e7e8ad06676e", "sha256_in_prefix": "031d1cb61ff4c11c80f2c2d063008524528caf089efd3b4fd7c3e7e8ad06676e", "size_in_bytes": 112925}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "71f0175a90055aa49c03f4f1f068f8bee0b350c4b86915d6172c8931cc718624", "sha256_in_prefix": "71f0175a90055aa49c03f4f1f068f8bee0b350c4b86915d6172c8931cc718624", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-312.pyc", "path_type": "hardlink", "sha256": "30b010f91c5a793616adf99bbe4f0c8c08f00d51ef35d4a2209ae30741f7b525", "sha256_in_prefix": "30b010f91c5a793616adf99bbe4f0c8c08f00d51ef35d4a2209ae30741f7b525", "size_in_bytes": 323}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-312.pyc", "path_type": "hardlink", "sha256": "dc9933d081b95b02f2e83ec3ab19dafc79ae314cb30a099932809ed8fe9cc417", "sha256_in_prefix": "dc9933d081b95b02f2e83ec3ab19dafc79ae314cb30a099932809ed8fe9cc417", "size_in_bytes": 14232}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "c2fe8c0459e87d3539ba814b12b9129f19569442eda66f2e5d5b475f6fb9e43b", "sha256_in_prefix": "c2fe8c0459e87d3539ba814b12b9129f19569442eda66f2e5d5b475f6fb9e43b", "size_in_bytes": 39772}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "e62747ed2d21a1e65ae8af6cc9df0aca78aeec94e9a0d319791842c07149cbed", "sha256_in_prefix": "e62747ed2d21a1e65ae8af6cc9df0aca78aeec94e9a0d319791842c07149cbed", "size_in_bytes": 22870}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "9acae396acebf03c5a3ebafbc2ef7fa1bf167903a3645a57c3c6f5d448297109", "sha256_in_prefix": "9acae396acebf03c5a3ebafbc2ef7fa1bf167903a3645a57c3c6f5d448297109", "size_in_bytes": 25454}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-312.pyc", "path_type": "hardlink", "sha256": "5682ca2831eae6334d10a080f4ee5263060baa790786ea752dfa38b582a7824e", "sha256_in_prefix": "5682ca2831eae6334d10a080f4ee5263060baa790786ea752dfa38b582a7824e", "size_in_bytes": 1075}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-312.pyc", "path_type": "hardlink", "sha256": "5a670cf029419d5bbd32897ca284b3dc4182b797100693d15350e85bfc0d18c1", "sha256_in_prefix": "5a670cf029419d5bbd32897ca284b3dc4182b797100693d15350e85bfc0d18c1", "size_in_bytes": 1447}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-312.pyc", "path_type": "hardlink", "sha256": "d748562ef10289b3932e1d170e9c6f3a0a3881c058e12decd7270239d0ed2c80", "sha256_in_prefix": "d748562ef10289b3932e1d170e9c6f3a0a3881c058e12decd7270239d0ed2c80", "size_in_bytes": 871}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-312.pyc", "path_type": "hardlink", "sha256": "4af2c105f2e3684e0ae64b23096ec193157a1161444a23e1ffd8b69521bbaaf1", "sha256_in_prefix": "4af2c105f2e3684e0ae64b23096ec193157a1161444a23e1ffd8b69521bbaaf1", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-312.pyc", "path_type": "hardlink", "sha256": "84a4a07c3430cebf300bff47bfd6fa56b357da27ba604e53c227086f7a3ac774", "sha256_in_prefix": "84a4a07c3430cebf300bff47bfd6fa56b357da27ba604e53c227086f7a3ac774", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "e8dcd0fd67edc71cd58452aaff1798cb15cb7fb98a2d26359013f91f177946f8", "sha256_in_prefix": "e8dcd0fd67edc71cd58452aaff1798cb15cb7fb98a2d26359013f91f177946f8", "size_in_bytes": 314}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-312.pyc", "path_type": "hardlink", "sha256": "fd3fbf7b61de4be0b55b1103fe120b191722e3b8463ff43ceba1091bb450442b", "sha256_in_prefix": "fd3fbf7b61de4be0b55b1103fe120b191722e3b8463ff43ceba1091bb450442b", "size_in_bytes": 173776}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-312.pyc", "path_type": "hardlink", "sha256": "ff6ed359086710d841b141851b233d3afd2a241d81893cbbccaa49dbbd0ec340", "sha256_in_prefix": "ff6ed359086710d841b141851b233d3afd2a241d81893cbbccaa49dbbd0ec340", "size_in_bytes": 36227}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "sha256_in_prefix": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "size_in_bytes": 3204}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "sha256_in_prefix": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "size_in_bytes": 2009}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "sha256_in_prefix": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "size_in_bytes": 494}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "21a99b31ae3e5a25282add7f81337f4412bbfb40b2855b595bc09eb0d9077690", "sha256_in_prefix": "21a99b31ae3e5a25282add7f81337f4412bbfb40b2855b595bc09eb0d9077690", "size_in_bytes": 520}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-312.pyc", "path_type": "hardlink", "sha256": "3796f84d2015e638b4382a10ce6bc2231fb0888a08cb750fe2dcece790a48d4b", "sha256_in_prefix": "3796f84d2015e638b4382a10ce6bc2231fb0888a08cb750fe2dcece790a48d4b", "size_in_bytes": 4985}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-312.pyc", "path_type": "hardlink", "sha256": "7668e03741040eb7140ca502736f1b49b448733e8c1c3683c4bd3cbad8f0b8f1", "sha256_in_prefix": "7668e03741040eb7140ca502736f1b49b448733e8c1c3683c4bd3cbad8f0b8f1", "size_in_bytes": 9673}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-312.pyc", "path_type": "hardlink", "sha256": "d6126ad758a32ed10da9f63d63ed32e5677cef70f5cc7ddb2e75fa33025d1bc0", "sha256_in_prefix": "d6126ad758a32ed10da9f63d63ed32e5677cef70f5cc7ddb2e75fa33025d1bc0", "size_in_bytes": 4516}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-312.pyc", "path_type": "hardlink", "sha256": "2af201fc7c56a4feb767a26fff36073b437ec8b139d01a4c046e02d548e2fcbd", "sha256_in_prefix": "2af201fc7c56a4feb767a26fff36073b437ec8b139d01a4c046e02d548e2fcbd", "size_in_bytes": 13947}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-312.pyc", "path_type": "hardlink", "sha256": "d54577e4512aa6a9bfc13bb57d7c31ec2faf1b8554873fc915a08177ce293ef9", "sha256_in_prefix": "d54577e4512aa6a9bfc13bb57d7c31ec2faf1b8554873fc915a08177ce293ef9", "size_in_bytes": 3203}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-312.pyc", "path_type": "hardlink", "sha256": "1c25194be63fea3d33ac440edc58a9597b4fbe3cdf751869ec8c96c4a9f8c450", "sha256_in_prefix": "1c25194be63fea3d33ac440edc58a9597b4fbe3cdf751869ec8c96c4a9f8c450", "size_in_bytes": 7877}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-312.pyc", "path_type": "hardlink", "sha256": "bed6ab0e8167398fa228192c59262c9ddeb2c4631fba2426f221bd7048542adc", "sha256_in_prefix": "bed6ab0e8167398fa228192c59262c9ddeb2c4631fba2426f221bd7048542adc", "size_in_bytes": 11336}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-312.pyc", "path_type": "hardlink", "sha256": "6b2327756396b85674fc2edc3f97443588951fbf790ccf418037391510a32356", "sha256_in_prefix": "6b2327756396b85674fc2edc3f97443588951fbf790ccf418037391510a32356", "size_in_bytes": 27174}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-312.pyc", "path_type": "hardlink", "sha256": "cf0a883162134c3917a2193d5cb2c6d3d0fcd0ea18184d31b90c12eaf0b5d32a", "sha256_in_prefix": "cf0a883162134c3917a2193d5cb2c6d3d0fcd0ea18184d31b90c12eaf0b5d32a", "size_in_bytes": 4372}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-312.pyc", "path_type": "hardlink", "sha256": "39d292276c1e7b82b65c622c6bca5e3348c422e74924d83521593358a2cfaac9", "sha256_in_prefix": "39d292276c1e7b82b65c622c6bca5e3348c422e74924d83521593358a2cfaac9", "size_in_bytes": 38980}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-312.pyc", "path_type": "hardlink", "sha256": "05a1bbe55cd239c73284f8cc933a97d2a2ae73d269e7c10aadd43205a1cdbc2b", "sha256_in_prefix": "05a1bbe55cd239c73284f8cc933a97d2a2ae73d269e7c10aadd43205a1cdbc2b", "size_in_bytes": 22967}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-312.pyc", "path_type": "hardlink", "sha256": "65347052105d120971a4b5a77ba89d8f83ea071a263d3eb070bf207a59754ea7", "sha256_in_prefix": "65347052105d120971a4b5a77ba89d8f83ea071a263d3eb070bf207a59754ea7", "size_in_bytes": 6597}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-312.pyc", "path_type": "hardlink", "sha256": "c5d8340ecac496814f7fe30a79233e3ff8b4f46b7fa0384eaea57258a63ad1fa", "sha256_in_prefix": "c5d8340ecac496814f7fe30a79233e3ff8b4f46b7fa0384eaea57258a63ad1fa", "size_in_bytes": 20436}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "sha256_in_prefix": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "size_in_bytes": 3306}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "sha256_in_prefix": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "size_in_bytes": 9612}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "sha256_in_prefix": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "size_in_bytes": 5715}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "d3af0dc6df7efc153ff8afbd8e057a99224521e86ae3b9181d927be9d846b433", "sha256_in_prefix": "d3af0dc6df7efc153ff8afbd8e057a99224521e86ae3b9181d927be9d846b433", "size_in_bytes": 4070}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-312.pyc", "path_type": "hardlink", "sha256": "f5ca9c228d635cd04b7e2d6c53600366467e6f3012741782134ad65820f6a4ac", "sha256_in_prefix": "f5ca9c228d635cd04b7e2d6c53600366467e6f3012741782134ad65820f6a4ac", "size_in_bytes": 47326}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "sha256_in_prefix": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "size_in_bytes": 10561}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "sha256_in_prefix": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "size_in_bytes": 34762}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "sha256_in_prefix": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "size_in_bytes": 40074}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "sha256_in_prefix": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "size_in_bytes": 21014}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "sha256_in_prefix": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "size_in_bytes": 16676}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "46f574560ab365f292bf07e41c448d1f2d4b93345c34eacef6e1f5998ea967c9", "sha256_in_prefix": "46f574560ab365f292bf07e41c448d1f2d4b93345c34eacef6e1f5998ea967c9", "size_in_bytes": 19743}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-312.pyc", "path_type": "hardlink", "sha256": "3af5209fbd7db2cb853e52d333e9fc74d546ef6bb6aaa1bdcbcdbfaf59dfff78", "sha256_in_prefix": "3af5209fbd7db2cb853e52d333e9fc74d546ef6bb6aaa1bdcbcdbfaf59dfff78", "size_in_bytes": 1898}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-312.pyc", "path_type": "hardlink", "sha256": "24d89985d38a35d98aa3efaea4c61f1eed655b8942dfd4c214958fcfadf767e1", "sha256_in_prefix": "24d89985d38a35d98aa3efaea4c61f1eed655b8942dfd4c214958fcfadf767e1", "size_in_bytes": 10659}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-312.pyc", "path_type": "hardlink", "sha256": "854791058fe2733735c0983adc3ab42a3f0a3d61b3a7846a4859cf0b7311cbc0", "sha256_in_prefix": "854791058fe2733735c0983adc3ab42a3f0a3d61b3a7846a4859cf0b7311cbc0", "size_in_bytes": 12873}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-312.pyc", "path_type": "hardlink", "sha256": "1e661df73c8e8cb4b40df912f5c0bd580cc42ef8e0e6a513347d7c9b0b32a1d2", "sha256_in_prefix": "1e661df73c8e8cb4b40df912f5c0bd580cc42ef8e0e6a513347d7c9b0b32a1d2", "size_in_bytes": 7969}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-312.pyc", "path_type": "hardlink", "sha256": "0bfa1e516f83962185b0a2efaed6b7871f90fa5f1faba919ae3699a9daee1a4b", "sha256_in_prefix": "0bfa1e516f83962185b0a2efaed6b7871f90fa5f1faba919ae3699a9daee1a4b", "size_in_bytes": 14999}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-312.pyc", "path_type": "hardlink", "sha256": "d7c734abd755da740fab31b4fc884bf6cb1fe41305de7dc64713555c168b4393", "sha256_in_prefix": "d7c734abd755da740fab31b4fc884bf6cb1fe41305de7dc64713555c168b4393", "size_in_bytes": 559}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-312.pyc", "path_type": "hardlink", "sha256": "7744e0fd623ce40eec14cbf030e3bb36a961fcf50f9c0d8bdb85cd183700c39a", "sha256_in_prefix": "7744e0fd623ce40eec14cbf030e3bb36a961fcf50f9c0d8bdb85cd183700c39a", "size_in_bytes": 13636}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "23260ff38b41b24325537dea96eba57aac58653cee6a27e324f025864132afa7", "sha256_in_prefix": "23260ff38b41b24325537dea96eba57aac58653cee6a27e324f025864132afa7", "size_in_bytes": 347}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-312.pyc", "path_type": "hardlink", "sha256": "b99bf7b0ebf888e861ee324d37ef184a02517cc791bc6d3b08e15061d567237d", "sha256_in_prefix": "b99bf7b0ebf888e861ee324d37ef184a02517cc791bc6d3b08e15061d567237d", "size_in_bytes": 26862}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-312.pyc", "path_type": "hardlink", "sha256": "b1f176472ddfbc7f81b3c1d38ab1debc9de115550660be7406104cdfa7db74a7", "sha256_in_prefix": "b1f176472ddfbc7f81b3c1d38ab1debc9de115550660be7406104cdfa7db74a7", "size_in_bytes": 3871}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-312.pyc", "path_type": "hardlink", "sha256": "7c498b474b7d23a70e3ba3c66da5029d523f60ea5aa16d15a33ff13c7335f5b7", "sha256_in_prefix": "7c498b474b7d23a70e3ba3c66da5029d523f60ea5aa16d15a33ff13c7335f5b7", "size_in_bytes": 329}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "2e05173196f952a85ed5bf424562cf67cc81f714a2eb845cc15517ba3e081cf5", "sha256_in_prefix": "2e05173196f952a85ed5bf424562cf67cc81f714a2eb845cc15517ba3e081cf5", "size_in_bytes": 2037}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-312.pyc", "path_type": "hardlink", "sha256": "d37d541b1011a0e2f7f5111c1ce8f1516b510b04a2fa24640f741ea7303843b5", "sha256_in_prefix": "d37d541b1011a0e2f7f5111c1ce8f1516b510b04a2fa24640f741ea7303843b5", "size_in_bytes": 34836}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-312.pyc", "path_type": "hardlink", "sha256": "e00a8abaec358296d5c956ed25835043b370e4c585948c64ed623ddc9ba6233e", "sha256_in_prefix": "e00a8abaec358296d5c956ed25835043b370e4c585948c64ed623ddc9ba6233e", "size_in_bytes": 3816}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-312.pyc", "path_type": "hardlink", "sha256": "fec57fd5a47711c0b6e512e9acc7cb01f70001d0325b3dadc792852cd243225f", "sha256_in_prefix": "fec57fd5a47711c0b6e512e9acc7cb01f70001d0325b3dadc792852cd243225f", "size_in_bytes": 10524}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-312.pyc", "path_type": "hardlink", "sha256": "10539b437d9948bf177f1f091da6f8dfd569f5a09125b0090a02aa365e60fd63", "sha256_in_prefix": "10539b437d9948bf177f1f091da6f8dfd569f5a09125b0090a02aa365e60fd63", "size_in_bytes": 2723}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-312.pyc", "path_type": "hardlink", "sha256": "3dd4e599a397dd3b2d51f2509a91f29cf5730e5a3f4bb1e8dbcc3779bb8109c7", "sha256_in_prefix": "3dd4e599a397dd3b2d51f2509a91f29cf5730e5a3f4bb1e8dbcc3779bb8109c7", "size_in_bytes": 12215}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-312.pyc", "path_type": "hardlink", "sha256": "0846c4a731a66e3beeecf63db295a1d8bb48a5eb6a49c7d890fa5ab779807618", "sha256_in_prefix": "0846c4a731a66e3beeecf63db295a1d8bb48a5eb6a49c7d890fa5ab779807618", "size_in_bytes": 9105}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-312.pyc", "path_type": "hardlink", "sha256": "1e0936d9bff96b157fe2d6fb2b1535c6cad0d60fb125b8a97e20aea369e4fad9", "sha256_in_prefix": "1e0936d9bff96b157fe2d6fb2b1535c6cad0d60fb125b8a97e20aea369e4fad9", "size_in_bytes": 1710}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-312.pyc", "path_type": "hardlink", "sha256": "e801ac596918a3f3d0028bca7a550d058923d4b08323b8a40cd8ee501e5831fe", "sha256_in_prefix": "e801ac596918a3f3d0028bca7a550d058923d4b08323b8a40cd8ee501e5831fe", "size_in_bytes": 5506}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-312.pyc", "path_type": "hardlink", "sha256": "b6ed58ad376eeada3bca258bff9c75bcb6a80308169519a26fa1021a078cc893", "sha256_in_prefix": "b6ed58ad376eeada3bca258bff9c75bcb6a80308169519a26fa1021a078cc893", "size_in_bytes": 3358}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-312.pyc", "path_type": "hardlink", "sha256": "a2cecae8546e1c4864849e23fcedf0a5668b790dcd0dea1f466187717f8184e5", "sha256_in_prefix": "a2cecae8546e1c4864849e23fcedf0a5668b790dcd0dea1f466187717f8184e5", "size_in_bytes": 51839}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-312.pyc", "path_type": "hardlink", "sha256": "8492843f2555a44bf30b01f287de0de492576b479576bc23cf43d56df1050832", "sha256_in_prefix": "8492843f2555a44bf30b01f287de0de492576b479576bc23cf43d56df1050832", "size_in_bytes": 2368}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-312.pyc", "path_type": "hardlink", "sha256": "668157ae4dad971fd4f102f982a40e0593afbee6f46ce63cdd746efb6ab4a85a", "sha256_in_prefix": "668157ae4dad971fd4f102f982a40e0593afbee6f46ce63cdd746efb6ab4a85a", "size_in_bytes": 7647}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "sha256_in_prefix": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "sha256_in_prefix": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "size_in_bytes": 4900}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "6ec12ab0183d9bdd88cdc2843e60b1213a96bb28bbd9e4c33e94a3beea43065a", "sha256_in_prefix": "6ec12ab0183d9bdd88cdc2843e60b1213a96bb28bbd9e4c33e94a3beea43065a", "size_in_bytes": 230}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-312.pyc", "path_type": "hardlink", "sha256": "fcfd2671a74a25f199c33642167516cc48e9663fbd5160bd11bb2239526d2626", "sha256_in_prefix": "fcfd2671a74a25f199c33642167516cc48e9663fbd5160bd11bb2239526d2626", "size_in_bytes": 965}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "60bcf3b4be43372ec3004c684fc9aad273a3c91b0ce1758db49292295ed9feba", "sha256_in_prefix": "60bcf3b4be43372ec3004c684fc9aad273a3c91b0ce1758db49292295ed9feba", "size_in_bytes": 25934}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "path_type": "hardlink", "sha256": "b6159a7aa9024849c08126ef8b862fb1a4f7d9c28adc1c8bc863e959558b4d74", "sha256_in_prefix": "b6159a7aa9024849c08126ef8b862fb1a4f7d9c28adc1c8bc863e959558b4d74", "size_in_bytes": 1376}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "477f0a0434bf0bfc3e5fdaafcd1812f9226b89a28062c0fc089a341911ffe3c5", "sha256_in_prefix": "477f0a0434bf0bfc3e5fdaafcd1812f9226b89a28062c0fc089a341911ffe3c5", "size_in_bytes": 742}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "path_type": "hardlink", "sha256": "ba140e7aaaf8737393a5d49658338ec36e375804e682f00eb911738125f32a1a", "sha256_in_prefix": "ba140e7aaaf8737393a5d49658338ec36e375804e682f00eb911738125f32a1a", "size_in_bytes": 16184}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-312.pyc", "path_type": "hardlink", "sha256": "15e2fb74074d0d50345078e9f426257557d2ae89965e6096ba74492f81493fd5", "sha256_in_prefix": "15e2fb74074d0d50345078e9f426257557d2ae89965e6096ba74492f81493fd5", "size_in_bytes": 8595}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-312.pyc", "path_type": "hardlink", "sha256": "a494e44ce19fdfe325e42387247d9f63820ea5519d61f07c08a7fa89f6d6dd6b", "sha256_in_prefix": "a494e44ce19fdfe325e42387247d9f63820ea5519d61f07c08a7fa89f6d6dd6b", "size_in_bytes": 918}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-312.pyc", "path_type": "hardlink", "sha256": "dbc4295d7477d187be9430f6e7c9887de63aa0b73f1d284dffa2bdb8dcffb7bc", "sha256_in_prefix": "dbc4295d7477d187be9430f6e7c9887de63aa0b73f1d284dffa2bdb8dcffb7bc", "size_in_bytes": 11422}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "3a686afcb0049ce33ceafccc43f5db11a677a8e63c0234a45c86095dd723aab8", "sha256_in_prefix": "3a686afcb0049ce33ceafccc43f5db11a677a8e63c0234a45c86095dd723aab8", "size_in_bytes": 6893}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-312.pyc", "path_type": "hardlink", "sha256": "a1ae19f9f0025aaf573715abd6ce1baea71c0be9f49f01059856b8c877e05067", "sha256_in_prefix": "a1ae19f9f0025aaf573715abd6ce1baea71c0be9f49f01059856b8c877e05067", "size_in_bytes": 16035}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-312.pyc", "path_type": "hardlink", "sha256": "47df4c3d1a2491ef135c8d13c89aa14eb1862b2d3266fffe003397f47a89ee26", "sha256_in_prefix": "47df4c3d1a2491ef135c8d13c89aa14eb1862b2d3266fffe003397f47a89ee26", "size_in_bytes": 4416}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-312.pyc", "path_type": "hardlink", "sha256": "3ee3f0cf661f7d2d8316c6e22d239bb8cd4f3e5603e87991b89e3d52a1c0a3c8", "sha256_in_prefix": "3ee3f0cf661f7d2d8316c6e22d239bb8cd4f3e5603e87991b89e3d52a1c0a3c8", "size_in_bytes": 6682}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-312.pyc", "path_type": "hardlink", "sha256": "b4b0c576e6baaaddb436e79e388116d9d0ff2ff8f982657ff2a59b39a290b621", "sha256_in_prefix": "b4b0c576e6baaaddb436e79e388116d9d0ff2ff8f982657ff2a59b39a290b621", "size_in_bytes": 1481}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "9a8a9df61f6d9e763b540b156b85dd0a0d30fefe16087a3f164d94428e75eb20", "sha256_in_prefix": "9a8a9df61f6d9e763b540b156b85dd0a0d30fefe16087a3f164d94428e75eb20", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "593190ecf3b968019025b509ee2c0a78cd1aae5e5126e5d1b5266f3fe893e2ec", "sha256_in_prefix": "593190ecf3b968019025b509ee2c0a78cd1aae5e5126e5d1b5266f3fe893e2ec", "size_in_bytes": 170}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "path_type": "hardlink", "sha256": "f654fbdf0ea9246c6f2881d6a43840693c68d07ee94469d789d9cdccb31e579d", "sha256_in_prefix": "f654fbdf0ea9246c6f2881d6a43840693c68d07ee94469d789d9cdccb31e579d", "size_in_bytes": 4996}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "path_type": "hardlink", "sha256": "c58dab75b3127975d1ec82297e2513e57831216404688bfa5181c37c54b59bb0", "sha256_in_prefix": "c58dab75b3127975d1ec82297e2513e57831216404688bfa5181c37c54b59bb0", "size_in_bytes": 9823}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "path_type": "hardlink", "sha256": "ee8bd937772d0add7107e97a8b53287efb72905e09f5be5349e8570b407e4058", "sha256_in_prefix": "ee8bd937772d0add7107e97a8b53287efb72905e09f5be5349e8570b407e4058", "size_in_bytes": 4536}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "path_type": "hardlink", "sha256": "74533b545893dd6ef5d22b4308dddd8d9e79021e7e59a9f07605e71ae0f07bb8", "sha256_in_prefix": "74533b545893dd6ef5d22b4308dddd8d9e79021e7e59a9f07605e71ae0f07bb8", "size_in_bytes": 14026}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "path_type": "hardlink", "sha256": "2315a4a7ad6e105788d66f0d4bbfcd88288f4e568ec068a415a48573dd16d60b", "sha256_in_prefix": "2315a4a7ad6e105788d66f0d4bbfcd88288f4e568ec068a415a48573dd16d60b", "size_in_bytes": 3218}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "path_type": "hardlink", "sha256": "c193c03a3df187e01b832b5a2a4599841c421b481cdd41a4896c563af0a50d90", "sha256_in_prefix": "c193c03a3df187e01b832b5a2a4599841c421b481cdd41a4896c563af0a50d90", "size_in_bytes": 7918}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "path_type": "hardlink", "sha256": "88ecd22118d3761b6f786c144cb5d61ef2fa202ec5cc01473b10335534755ad7", "sha256_in_prefix": "88ecd22118d3761b6f786c144cb5d61ef2fa202ec5cc01473b10335534755ad7", "size_in_bytes": 10489}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "path_type": "hardlink", "sha256": "fee38852460d4ff9ca4732f21e5850cbba80edf0149b0a83d97cbcb5cc6a5ed1", "sha256_in_prefix": "fee38852460d4ff9ca4732f21e5850cbba80edf0149b0a83d97cbcb5cc6a5ed1", "size_in_bytes": 4431}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "path_type": "hardlink", "sha256": "44e6d1623e7a0f9e23b0f345cc374cdc2885bd454bcee77aeba71e17504e6d92", "sha256_in_prefix": "44e6d1623e7a0f9e23b0f345cc374cdc2885bd454bcee77aeba71e17504e6d92", "size_in_bytes": 39501}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "path_type": "hardlink", "sha256": "b09d8bd4051171ced4e90e19eb15ff1a6a9b627b197061d80e774535e07c3e25", "sha256_in_prefix": "b09d8bd4051171ced4e90e19eb15ff1a6a9b627b197061d80e774535e07c3e25", "size_in_bytes": 21629}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "path_type": "hardlink", "sha256": "f16ca7c407479a1890a264aa82f81d7fcd92590d6892d79a0c75f9e61d88dc70", "sha256_in_prefix": "f16ca7c407479a1890a264aa82f81d7fcd92590d6892d79a0c75f9e61d88dc70", "size_in_bytes": 7263}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "path_type": "hardlink", "sha256": "2b131c48497c723846ffe06d50f5c95afb93f27960856b276b918d432eb4a18f", "sha256_in_prefix": "2b131c48497c723846ffe06d50f5c95afb93f27960856b276b918d432eb4a18f", "size_in_bytes": 19979}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "822651e93c2e9a37638bcd8cf8cd2677f3d6a25ab008a15a3cbc64e6c8f985a4", "sha256_in_prefix": "822651e93c2e9a37638bcd8cf8cd2677f3d6a25ab008a15a3cbc64e6c8f985a4", "size_in_bytes": 22684}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-312.pyc", "path_type": "hardlink", "sha256": "96d8be7eb7d9fd506c55c3871a8f93c62a2fa04ea47d81bae21c46ac5c03f44f", "sha256_in_prefix": "96d8be7eb7d9fd506c55c3871a8f93c62a2fa04ea47d81bae21c46ac5c03f44f", "size_in_bytes": 5221}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "3419cbdda6924a7fea06a7f74e2a0c84b590cec83842483839c3b060bc89ba63", "sha256_in_prefix": "3419cbdda6924a7fea06a7f74e2a0c84b590cec83842483839c3b060bc89ba63", "size_in_bytes": 157}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-312.pyc", "path_type": "hardlink", "sha256": "6628b41282a666aa4d500ccd3ce13c6e1acabfe79044fa31e33529a14c22e315", "sha256_in_prefix": "6628b41282a666aa4d500ccd3ce13c6e1acabfe79044fa31e33529a14c22e315", "size_in_bytes": 468}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "Lib/site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "sha256_in_prefix": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "size_in_bytes": 7356}, {"_path": "Lib/site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "sha256_in_prefix": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "size_in_bytes": 20446}, {"_path": "Lib/site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "sha256_in_prefix": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "size_in_bytes": 803}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "0364fe7c3f86b733aea839f9a4a9a27f14abe49b9bb5c0abb564ce2af7884b74", "sha256_in_prefix": "0364fe7c3f86b733aea839f9a4a9a27f14abe49b9bb5c0abb564ce2af7884b74", "size_in_bytes": 609}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-312.pyc", "path_type": "hardlink", "sha256": "0a2d6cbb70e2f94989e1d98ec9e40a1092634c8522a4fde37a2e6defa89cc036", "sha256_in_prefix": "0a2d6cbb70e2f94989e1d98ec9e40a1092634c8522a4fde37a2e6defa89cc036", "size_in_bytes": 6530}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-312.pyc", "path_type": "hardlink", "sha256": "2e55eb18d1c110bee2fa3b88c93eed9e0e2180b456d1772f3e6fed4f9aaeda46", "sha256_in_prefix": "2e55eb18d1c110bee2fa3b88c93eed9e0e2180b456d1772f3e6fed4f9aaeda46", "size_in_bytes": 3491}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-312.pyc", "path_type": "hardlink", "sha256": "4cbbaa85f0926745f2db94e10de942821bde5ce1386b06f8c66cd21f1731443f", "sha256_in_prefix": "4cbbaa85f0926745f2db94e10de942821bde5ce1386b06f8c66cd21f1731443f", "size_in_bytes": 24258}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-312.pyc", "path_type": "hardlink", "sha256": "10730765b26c24a30cef2baf51a1b5e236f2b198d20bb8247e674fd970219c1d", "sha256_in_prefix": "10730765b26c24a30cef2baf51a1b5e236f2b198d20bb8247e674fd970219c1d", "size_in_bytes": 2055}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "7ee388060814793d9979be80c6950e7fb9e7a68a65aa14c89536caa1d0fb1ed4", "sha256_in_prefix": "7ee388060814793d9979be80c6950e7fb9e7a68a65aa14c89536caa1d0fb1ed4", "size_in_bytes": 26109}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build.cpython-312.pyc", "path_type": "hardlink", "sha256": "babe038588dd08cd2b7378952ddb7ed861aaa46ab31715aa0d4569cd2343aa16", "sha256_in_prefix": "babe038588dd08cd2b7378952ddb7ed861aaa46ab31715aa0d4569cd2343aa16", "size_in_bytes": 5395}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-312.pyc", "path_type": "hardlink", "sha256": "3846fee26eb987f6c81435543141e81ef96e068f08255c8d0ed47d798a5f23b5", "sha256_in_prefix": "3846fee26eb987f6c81435543141e81ef96e068f08255c8d0ed47d798a5f23b5", "size_in_bytes": 3778}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-312.pyc", "path_type": "hardlink", "sha256": "d8263afc1c90ff822ccecb5792defc5af45cd492bcf147544d0031e074096e89", "sha256_in_prefix": "d8263afc1c90ff822ccecb5792defc5af45cd492bcf147544d0031e074096e89", "size_in_bytes": 22919}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-312.pyc", "path_type": "hardlink", "sha256": "871ccbb272fa60899445b1fb39b78da3ee2f18e4aae7d1592bc67629797004f2", "sha256_in_prefix": "871ccbb272fa60899445b1fb39b78da3ee2f18e4aae7d1592bc67629797004f2", "size_in_bytes": 21864}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-312.pyc", "path_type": "hardlink", "sha256": "2687b73010679cb790ab06c49c1880ba532b2e8461f0b85d312c0779e3a0c648", "sha256_in_prefix": "2687b73010679cb790ab06c49c1880ba532b2e8461f0b85d312c0779e3a0c648", "size_in_bytes": 10006}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-312.pyc", "path_type": "hardlink", "sha256": "26b70198aaf1e63e7a4c2910160b7162535f2307d8acf47e1419b23f014fed40", "sha256_in_prefix": "26b70198aaf1e63e7a4c2910160b7162535f2307d8acf47e1419b23f014fed40", "size_in_bytes": 5110}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-312.pyc", "path_type": "hardlink", "sha256": "0a7a27dba3594d18d9748a24c76b7028479f8ca8f13e72397bb4737ea8a17e20", "sha256_in_prefix": "0a7a27dba3594d18d9748a24c76b7028479f8ca8f13e72397bb4737ea8a17e20", "size_in_bytes": 110198}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "b7fa71b65dbcb88bc0e379a0bbb01c99fd56601c051ac1ca214ef63d763a97d4", "sha256_in_prefix": "b7fa71b65dbcb88bc0e379a0bbb01c99fd56601c051ac1ca214ef63d763a97d4", "size_in_bytes": 49090}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-312.pyc", "path_type": "hardlink", "sha256": "b53d11bd451ff0de41b8575f3fa9b7b999d8cad3b84789a05c12355ef6d970ae", "sha256_in_prefix": "b53d11bd451ff0de41b8575f3fa9b7b999d8cad3b84789a05c12355ef6d970ae", "size_in_bytes": 34330}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install.cpython-312.pyc", "path_type": "hardlink", "sha256": "8bd6bf490e05b6606dc232a925e05fa3d0dfe49570a32eeefd3678aec1fe9bd6", "sha256_in_prefix": "8bd6bf490e05b6606dc232a925e05fa3d0dfe49570a32eeefd3678aec1fe9bd6", "size_in_bytes": 7650}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-312.pyc", "path_type": "hardlink", "sha256": "1025280353c0a1fd6047b7b8ae20f055ec70c110b64a784477f209577e333bec", "sha256_in_prefix": "1025280353c0a1fd6047b7b8ae20f055ec70c110b64a784477f209577e333bec", "size_in_bytes": 3715}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-312.pyc", "path_type": "hardlink", "sha256": "54144f4a774997b23101c7137ecd99e991f6b49762765adc788cacd3957b6c82", "sha256_in_prefix": "54144f4a774997b23101c7137ecd99e991f6b49762765adc788cacd3957b6c82", "size_in_bytes": 6065}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-312.pyc", "path_type": "hardlink", "sha256": "27456b8a06c1103a41b4f812ee8698cf5aa850a5c9a7febece22784adfb53ffe", "sha256_in_prefix": "27456b8a06c1103a41b4f812ee8698cf5aa850a5c9a7febece22784adfb53ffe", "size_in_bytes": 3901}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-312.pyc", "path_type": "hardlink", "sha256": "7bfad66f6b2b968bdcc8dc1d22b04384b22a3964162f26d91991471877737197", "sha256_in_prefix": "7bfad66f6b2b968bdcc8dc1d22b04384b22a3964162f26d91991471877737197", "size_in_bytes": 3679}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-312.pyc", "path_type": "hardlink", "sha256": "d7b5947dc7c405f0343ed5c0a9812fd66e985bbbe116eaba4766f72b98c32e1d", "sha256_in_prefix": "d7b5947dc7c405f0343ed5c0a9812fd66e985bbbe116eaba4766f72b98c32e1d", "size_in_bytes": 1220}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-312.pyc", "path_type": "hardlink", "sha256": "410be301f3f5257b5d4aae57afda09f57877e11414d65c79079439bbbb345241", "sha256_in_prefix": "410be301f3f5257b5d4aae57afda09f57877e11414d65c79079439bbbb345241", "size_in_bytes": 11995}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-312.pyc", "path_type": "hardlink", "sha256": "d1887dafa5ba4028147f8b0026d4c8e7598b77149aeeba500f8e60e36e87efad", "sha256_in_prefix": "d1887dafa5ba4028147f8b0026d4c8e7598b77149aeeba500f8e60e36e87efad", "size_in_bytes": 6993}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/test.cpython-312.pyc", "path_type": "hardlink", "sha256": "ee05d712b09ab2b05bc10c928993f9476d72a0f9377c465bd064aa52b8b74dfe", "sha256_in_prefix": "ee05d712b09ab2b05bc10c928993f9476d72a0f9377c465bd064aa52b8b74dfe", "size_in_bytes": 1846}, {"_path": "Lib/site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "sha256_in_prefix": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "size_in_bytes": 4228}, {"_path": "Lib/site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "sha256_in_prefix": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "size_in_bytes": 2380}, {"_path": "Lib/site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "sha256_in_prefix": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "size_in_bytes": 16972}, {"_path": "Lib/site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "sha256_in_prefix": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "size_in_bytes": 1435}, {"_path": "Lib/site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "sha256_in_prefix": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "size_in_bytes": 22246}, {"_path": "Lib/site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "sha256_in_prefix": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "size_in_bytes": 6052}, {"_path": "Lib/site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "sha256_in_prefix": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "size_in_bytes": 4528}, {"_path": "Lib/site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "sha256_in_prefix": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "size_in_bytes": 18377}, {"_path": "Lib/site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "sha256_in_prefix": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "size_in_bytes": 15539}, {"_path": "Lib/site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "sha256_in_prefix": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "size_in_bytes": 6886}, {"_path": "Lib/site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "sha256_in_prefix": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "size_in_bytes": 3450}, {"_path": "Lib/site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "sha256_in_prefix": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "size_in_bytes": 87870}, {"_path": "Lib/site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "sha256_in_prefix": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "size_in_bytes": 35624}, {"_path": "Lib/site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "sha256_in_prefix": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "size_in_bytes": 25982}, {"_path": "Lib/site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "sha256_in_prefix": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "size_in_bytes": 7046}, {"_path": "Lib/site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "sha256_in_prefix": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "size_in_bytes": 2075}, {"_path": "Lib/site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "sha256_in_prefix": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "size_in_bytes": 4319}, {"_path": "Lib/site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "sha256_in_prefix": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "size_in_bytes": 2637}, {"_path": "Lib/site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "Lib/site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "sha256_in_prefix": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "size_in_bytes": 2187}, {"_path": "Lib/site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "sha256_in_prefix": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "size_in_bytes": 692}, {"_path": "Lib/site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "sha256_in_prefix": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "size_in_bytes": 7374}, {"_path": "Lib/site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "sha256_in_prefix": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "size_in_bytes": 5100}, {"_path": "Lib/site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "Lib/site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "0d0b936396338616a18f989278b906ebc383b44255caf780edf18e2515083407", "sha256_in_prefix": "0d0b936396338616a18f989278b906ebc383b44255caf780edf18e2515083407", "size_in_bytes": 144}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-312.pyc", "path_type": "hardlink", "sha256": "d610aae66bd720bc233dc3e65ccc35dddfd797d7cd09df1bfe85ea64e2798598", "sha256_in_prefix": "d610aae66bd720bc233dc3e65ccc35dddfd797d7cd09df1bfe85ea64e2798598", "size_in_bytes": 298}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-312.pyc", "path_type": "hardlink", "sha256": "07f678a4cbec8aa6b13bc166800d06f996e9421ea67e3abfb32bd27959e32216", "sha256_in_prefix": "07f678a4cbec8aa6b13bc166800d06f996e9421ea67e3abfb32bd27959e32216", "size_in_bytes": 1387}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-312.pyc", "path_type": "hardlink", "sha256": "e5367b708acae7b8c96788f1ab86969378ad358a87baa772812a6a1bd1c7b253", "sha256_in_prefix": "e5367b708acae7b8c96788f1ab86969378ad358a87baa772812a6a1bd1c7b253", "size_in_bytes": 427}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-312.pyc", "path_type": "hardlink", "sha256": "a9e6406d19722caa505d5c97e58451c7a4a11b41abcdcbaa75f3d239d2d15415", "sha256_in_prefix": "a9e6406d19722caa505d5c97e58451c7a4a11b41abcdcbaa75f3d239d2d15415", "size_in_bytes": 273}, {"_path": "Lib/site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "Lib/site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "Lib/site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "Lib/site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "24c8f50e838f267004502b26a1779cdca767653b086d10f2bdce0b1d310bd753", "sha256_in_prefix": "24c8f50e838f267004502b26a1779cdca767653b086d10f2bdce0b1d310bd753", "size_in_bytes": 1969}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-312.pyc", "path_type": "hardlink", "sha256": "b0c1aebadc797a202d7646a3dc178c3b478bd4d195aeace4e41e68fd6678d2ae", "sha256_in_prefix": "b0c1aebadc797a202d7646a3dc178c3b478bd4d195aeace4e41e68fd6678d2ae", "size_in_bytes": 25250}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-312.pyc", "path_type": "hardlink", "sha256": "704f8fe11eef44b0c1e0042031a2641e94d28521db1b117eeffbe6750fc2c61b", "sha256_in_prefix": "704f8fe11eef44b0c1e0042031a2641e94d28521db1b117eeffbe6750fc2c61b", "size_in_bytes": 24509}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-312.pyc", "path_type": "hardlink", "sha256": "facaef844d007679c8225484319357b4ad314b1179e92651013c8f0ace5a5a5c", "sha256_in_prefix": "facaef844d007679c8225484319357b4ad314b1179e92651013c8f0ace5a5a5c", "size_in_bytes": 23277}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-312.pyc", "path_type": "hardlink", "sha256": "1fda9f68f0b8bbc1cfbd4200218f74da85d4a1c2ae4c7ae2809b44df7e0b1411", "sha256_in_prefix": "1fda9f68f0b8bbc1cfbd4200218f74da85d4a1c2ae4c7ae2809b44df7e0b1411", "size_in_bytes": 33057}, {"_path": "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "sha256_in_prefix": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "size_in_bytes": 19120}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "sha256_in_prefix": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "size_in_bytes": 18737}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "286fc784fab4dee348f00bd166b8303b0e63cd49279631cf41c8cf8a37dd53e4", "sha256_in_prefix": "286fc784fab4dee348f00bd166b8303b0e63cd49279631cf41c8cf8a37dd53e4", "size_in_bytes": 1821}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-312.pyc", "path_type": "hardlink", "sha256": "85e3975d1a1acf3b234d8e7f86e79cb6aa35c9dd1db3afd755bdefec1832d0b1", "sha256_in_prefix": "85e3975d1a1acf3b234d8e7f86e79cb6aa35c9dd1db3afd755bdefec1832d0b1", "size_in_bytes": 18317}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-312.pyc", "path_type": "hardlink", "sha256": "ff736512cb7051d94ce587716ed8d86e7f657a580a1b764f342738dd5bdf5c9b", "sha256_in_prefix": "ff736512cb7051d94ce587716ed8d86e7f657a580a1b764f342738dd5bdf5c9b", "size_in_bytes": 3085}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-312.pyc", "path_type": "hardlink", "sha256": "ecfb2e84e42bfbaa84208bed85517808311d104c5ce2bd2ed1adc1bbf0e71d44", "sha256_in_prefix": "ecfb2e84e42bfbaa84208bed85517808311d104c5ce2bd2ed1adc1bbf0e71d44", "size_in_bytes": 2804}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-312.pyc", "path_type": "hardlink", "sha256": "a636a83f60d21b55a2429d15913cce5d7568775ea1ae5a509946bbf5bf7167d9", "sha256_in_prefix": "a636a83f60d21b55a2429d15913cce5d7568775ea1ae5a509946bbf5bf7167d9", "size_in_bytes": 233297}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-312.pyc", "path_type": "hardlink", "sha256": "aca9438ed93b2fdc4463e200124e94376897ce2433780b9493c3fd525f4c681b", "sha256_in_prefix": "aca9438ed93b2fdc4463e200124e94376897ce2433780b9493c3fd525f4c681b", "size_in_bytes": 18426}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "sha256_in_prefix": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "size_in_bytes": 2858}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "sha256_in_prefix": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "size_in_bytes": 354682}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "sha256_in_prefix": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "size_in_bytes": 13564}, {"_path": "Lib/site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "Lib/site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "sha256_in_prefix": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "size_in_bytes": 16041}, {"_path": "Lib/site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "sha256_in_prefix": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "size_in_bytes": 18320}, {"_path": "Lib/site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "sha256_in_prefix": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "size_in_bytes": 26575}, {"_path": "Lib/site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "Lib/site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "sha256_in_prefix": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "size_in_bytes": 5965}, {"_path": "Lib/site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "sha256_in_prefix": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "size_in_bytes": 21258}, {"_path": "Lib/site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "sha256_in_prefix": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "size_in_bytes": 44897}, {"_path": "Lib/site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "sha256_in_prefix": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "size_in_bytes": 3024}, {"_path": "Lib/site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "sha256_in_prefix": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "size_in_bytes": 6683}, {"_path": "Lib/site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "sha256_in_prefix": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "size_in_bytes": 6062}, {"_path": "Lib/site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "sha256_in_prefix": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "size_in_bytes": 5110}, {"_path": "Lib/site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "sha256_in_prefix": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "size_in_bytes": 820}, {"_path": "Lib/site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "sha256_in_prefix": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "size_in_bytes": 1261}, {"_path": "Lib/site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "sha256_in_prefix": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "sha256_in_prefix": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "sha256_in_prefix": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "size_in_bytes": 41631}, {"_path": "Lib/site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "sha256_in_prefix": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "size_in_bytes": 3171}, {"_path": "Lib/site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "sha256_in_prefix": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "size_in_bytes": 40519}, {"_path": "Lib/site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "sha256_in_prefix": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "size_in_bytes": 14906}, {"_path": "Lib/site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "Lib/site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "c64c98ac78a26c92d27789752c7dc8a6ba067b6bfd2663fdbe298b52037f63b8", "sha256_in_prefix": "c64c98ac78a26c92d27789752c7dc8a6ba067b6bfd2663fdbe298b52037f63b8", "size_in_bytes": 652}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-312.pyc", "path_type": "hardlink", "sha256": "c4efe6a0ca71189cf0932cb134260e1c8503206ed6e78293157f2ebd14f4fe34", "sha256_in_prefix": "c4efe6a0ca71189cf0932cb134260e1c8503206ed6e78293157f2ebd14f4fe34", "size_in_bytes": 7177}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-312.pyc", "path_type": "hardlink", "sha256": "5949cc923302edf9b66e74dcfd2c928b333da8bb63779cca0d1935e3be696280", "sha256_in_prefix": "5949cc923302edf9b66e74dcfd2c928b333da8bb63779cca0d1935e3be696280", "size_in_bytes": 3499}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-312.pyc", "path_type": "hardlink", "sha256": "a88cc3e071e292ef7bc28c1a05af0d8f995654226166fdfd0e9d849005fb87a7", "sha256_in_prefix": "a88cc3e071e292ef7bc28c1a05af0d8f995654226166fdfd0e9d849005fb87a7", "size_in_bytes": 7115}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-312.pyc", "path_type": "hardlink", "sha256": "847475df6a76af7bf3656d88543f62bb38341f8f1c8667b4b9925ca432a0bbaf", "sha256_in_prefix": "847475df6a76af7bf3656d88543f62bb38341f8f1c8667b4b9925ca432a0bbaf", "size_in_bytes": 178}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-312.pyc", "path_type": "hardlink", "sha256": "90c27eed04fbbf89b77d522543d5120f1cda471c5801b04e34a42e5826c01863", "sha256_in_prefix": "90c27eed04fbbf89b77d522543d5120f1cda471c5801b04e34a42e5826c01863", "size_in_bytes": 4158}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-312.pyc", "path_type": "hardlink", "sha256": "2034e3ad2ae6ce0ee777d6a5167b8bf3db90cbdc0762ee7d0a9edaf51d7b6aa2", "sha256_in_prefix": "2034e3ad2ae6ce0ee777d6a5167b8bf3db90cbdc0762ee7d0a9edaf51d7b6aa2", "size_in_bytes": 172}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-312.pyc", "path_type": "hardlink", "sha256": "2a55f20ab9960b303a57d65df57bd3d848f8d31c0dd2d98c1b9c454bc38da477", "sha256_in_prefix": "2a55f20ab9960b303a57d65df57bd3d848f8d31c0dd2d98c1b9c454bc38da477", "size_in_bytes": 4925}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-312.pyc", "path_type": "hardlink", "sha256": "939145a63bb8a3d09aa577e31e5f9327ea597c64e4a6602e81b2e5f2b9c7f171", "sha256_in_prefix": "939145a63bb8a3d09aa577e31e5f9327ea597c64e4a6602e81b2e5f2b9c7f171", "size_in_bytes": 1828}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-312.pyc", "path_type": "hardlink", "sha256": "1a30d665d86e99d4d1174502e1e77c563b2bccd53a7c86aade02d7663799d9ee", "sha256_in_prefix": "1a30d665d86e99d4d1174502e1e77c563b2bccd53a7c86aade02d7663799d9ee", "size_in_bytes": 1527}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-312.pyc", "path_type": "hardlink", "sha256": "77e504af010b006cd3f4bc7baf5328db48117b7f30703a27777f89e6610f094f", "sha256_in_prefix": "77e504af010b006cd3f4bc7baf5328db48117b7f30703a27777f89e6610f094f", "size_in_bytes": 4079}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "2ac702c4fec23fae2a61c6cfaf35d084658327228a742fed792ee50a579b3408", "sha256_in_prefix": "2ac702c4fec23fae2a61c6cfaf35d084658327228a742fed792ee50a579b3408", "size_in_bytes": 33340}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-312.pyc", "path_type": "hardlink", "sha256": "93d8a09d518ffe50712f1f45f7910927f77649529a8e28c92123aefeb48713d7", "sha256_in_prefix": "93d8a09d518ffe50712f1f45f7910927f77649529a8e28c92123aefeb48713d7", "size_in_bytes": 1557}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-312.pyc", "path_type": "hardlink", "sha256": "8d1dbc7fea3181e12299f54a3b7ad4fc4d0e49e4977252f70700b66e5a66b0ba", "sha256_in_prefix": "8d1dbc7fea3181e12299f54a3b7ad4fc4d0e49e4977252f70700b66e5a66b0ba", "size_in_bytes": 4102}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-312.pyc", "path_type": "hardlink", "sha256": "fa0ff0803714d963216333e9cf5c4d051c0e1c390d9a153fb4eef86c4cdd85b8", "sha256_in_prefix": "fa0ff0803714d963216333e9cf5c4d051c0e1c390d9a153fb4eef86c4cdd85b8", "size_in_bytes": 13353}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-312.pyc", "path_type": "hardlink", "sha256": "7552d25c82986a6f1a52056470226a562cfb965e6e46f4e404431be976f98fbc", "sha256_in_prefix": "7552d25c82986a6f1a52056470226a562cfb965e6e46f4e404431be976f98fbc", "size_in_bytes": 44055}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-312.pyc", "path_type": "hardlink", "sha256": "2bcb6eada167b1761422d29361f931c6c4d8b713baa0f6f85d6708ce4ea9cc8f", "sha256_in_prefix": "2bcb6eada167b1761422d29361f931c6c4d8b713baa0f6f85d6708ce4ea9cc8f", "size_in_bytes": 16964}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-312.pyc", "path_type": "hardlink", "sha256": "d51405a3d5c76b461746db2d136c9c9adc368a9d8a4d5cb756d5167bb9cc3a39", "sha256_in_prefix": "d51405a3d5c76b461746db2d136c9c9adc368a9d8a4d5cb756d5167bb9cc3a39", "size_in_bytes": 30498}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-312.pyc", "path_type": "hardlink", "sha256": "29aed45cde44a4d236d26528ca9069f0009d81713796167c24e968f27515b2f7", "sha256_in_prefix": "29aed45cde44a4d236d26528ca9069f0009d81713796167c24e968f27515b2f7", "size_in_bytes": 23049}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-312.pyc", "path_type": "hardlink", "sha256": "64ce432ae4aed9b26b6bdfe5391f977112bb5a21759301e6cced6d225db3ef93", "sha256_in_prefix": "64ce432ae4aed9b26b6bdfe5391f977112bb5a21759301e6cced6d225db3ef93", "size_in_bytes": 885}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-312.pyc", "path_type": "hardlink", "sha256": "4ddc06ec4d462ac269f474053c6ce622a6138a19c41b3757839634a5c75ceb5e", "sha256_in_prefix": "4ddc06ec4d462ac269f474053c6ce622a6138a19c41b3757839634a5c75ceb5e", "size_in_bytes": 8563}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-312.pyc", "path_type": "hardlink", "sha256": "c04512af10367026bd54019df67b75dfd1984c8d268c0b42b13bf314ed0c71c9", "sha256_in_prefix": "c04512af10367026bd54019df67b75dfd1984c8d268c0b42b13bf314ed0c71c9", "size_in_bytes": 10877}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-312.pyc", "path_type": "hardlink", "sha256": "fef743169753c419ffb8c9172d2cae11b61b1d4f8a793cb5aa84ae78ac0b78f9", "sha256_in_prefix": "fef743169753c419ffb8c9172d2cae11b61b1d4f8a793cb5aa84ae78ac0b78f9", "size_in_bytes": 11093}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-312.pyc", "path_type": "hardlink", "sha256": "382146661da6a8a23ade2676cc7b43aa3c736ee83729d0936370d08261ee42ac", "sha256_in_prefix": "382146661da6a8a23ade2676cc7b43aa3c736ee83729d0936370d08261ee42ac", "size_in_bytes": 8183}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-312.pyc", "path_type": "hardlink", "sha256": "f7e88b4cb7c7872a18c4faff49595de546d9029f068ea446f397b54eb1889664", "sha256_in_prefix": "f7e88b4cb7c7872a18c4faff49595de546d9029f068ea446f397b54eb1889664", "size_in_bytes": 70800}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-312.pyc", "path_type": "hardlink", "sha256": "865778c8cbfc97fa4c286da2488e322418db91cb47b85e160c66a7adedcc87e7", "sha256_in_prefix": "865778c8cbfc97fa4c286da2488e322418db91cb47b85e160c66a7adedcc87e7", "size_in_bytes": 57500}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-312.pyc", "path_type": "hardlink", "sha256": "95ab18d167827ea7f3c500c23081aa32b9019d2da65ab01e7f7842b90c8cf91d", "sha256_in_prefix": "95ab18d167827ea7f3c500c23081aa32b9019d2da65ab01e7f7842b90c8cf91d", "size_in_bytes": 47780}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-312.pyc", "path_type": "hardlink", "sha256": "151af0310c44e91afbf0bf26fdbe1ce930c25ae9c209b82c74a11b8c6d78dde1", "sha256_in_prefix": "151af0310c44e91afbf0bf26fdbe1ce930c25ae9c209b82c74a11b8c6d78dde1", "size_in_bytes": 800}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-312.pyc", "path_type": "hardlink", "sha256": "68b30ec7806f6298dc89f4e59de0f62c79e04eeb0bcc6856ee5492a504a39d24", "sha256_in_prefix": "68b30ec7806f6298dc89f4e59de0f62c79e04eeb0bcc6856ee5492a504a39d24", "size_in_bytes": 12137}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-312.pyc", "path_type": "hardlink", "sha256": "1fa615182a15e116eb790ea25f50dd499abfaad299632c4aceabff64de090854", "sha256_in_prefix": "1fa615182a15e116eb790ea25f50dd499abfaad299632c4aceabff64de090854", "size_in_bytes": 3758}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-312.pyc", "path_type": "hardlink", "sha256": "dfd9515c167a56d0d6f34ebb23c2883163ba801358a2321bba56881e76be7d30", "sha256_in_prefix": "dfd9515c167a56d0d6f34ebb23c2883163ba801358a2321bba56881e76be7d30", "size_in_bytes": 1217}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-312.pyc", "path_type": "hardlink", "sha256": "0572bde57310807e39c3b13dca7e347cee7a3e606b2ea2dfad4896905938584d", "sha256_in_prefix": "0572bde57310807e39c3b13dca7e347cee7a3e606b2ea2dfad4896905938584d", "size_in_bytes": 6106}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-312.pyc", "path_type": "hardlink", "sha256": "8769dff6cc7a031bc789b52054355ae3e0b34fafd91e4cd438cee51e69d2fcf1", "sha256_in_prefix": "8769dff6cc7a031bc789b52054355ae3e0b34fafd91e4cd438cee51e69d2fcf1", "size_in_bytes": 3144}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-312.pyc", "path_type": "hardlink", "sha256": "0cd0eb48ae2fb292a47b481db08ad07aa5cd1bb6fd3fbf617f56382d0f0000a4", "sha256_in_prefix": "0cd0eb48ae2fb292a47b481db08ad07aa5cd1bb6fd3fbf617f56382d0f0000a4", "size_in_bytes": 26681}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-312.pyc", "path_type": "hardlink", "sha256": "1327234127a5b0ff14f4dac0b41fc643346f633ba24d89d335b67303d602294c", "sha256_in_prefix": "1327234127a5b0ff14f4dac0b41fc643346f633ba24d89d335b67303d602294c", "size_in_bytes": 5464}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-312.pyc", "path_type": "hardlink", "sha256": "a4b9c49534f10b097e89a66551a5cc2d6c2dcd9d0743b1914efe38959ffd6aee", "sha256_in_prefix": "a4b9c49534f10b097e89a66551a5cc2d6c2dcd9d0743b1914efe38959ffd6aee", "size_in_bytes": 18529}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-312.pyc", "path_type": "hardlink", "sha256": "9b545574245d8988c4e5e87b88d40b90bf8e8aacc659a6e639e16cd592e0d0f9", "sha256_in_prefix": "9b545574245d8988c4e5e87b88d40b90bf8e8aacc659a6e639e16cd592e0d0f9", "size_in_bytes": 9500}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-312.pyc", "path_type": "hardlink", "sha256": "1f4dbfc180afd648a993e4304c9ecc916fad656db58861cba39b40e5c10d0d67", "sha256_in_prefix": "1f4dbfc180afd648a993e4304c9ecc916fad656db58861cba39b40e5c10d0d67", "size_in_bytes": 46022}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-312.pyc", "path_type": "hardlink", "sha256": "ce9d8e7b76c6dee76762d958126db016875256505d8392f79d6a0412e15e1ef7", "sha256_in_prefix": "ce9d8e7b76c6dee76762d958126db016875256505d8392f79d6a0412e15e1ef7", "size_in_bytes": 2820}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-312.pyc", "path_type": "hardlink", "sha256": "f83cdd5097ac35cb78fb8005c03f2c918cc6826ec4604deedc803e194d1f8cda", "sha256_in_prefix": "f83cdd5097ac35cb78fb8005c03f2c918cc6826ec4604deedc803e194d1f8cda", "size_in_bytes": 17512}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-312.pyc", "path_type": "hardlink", "sha256": "f2571d3da3c25523794205afb7253fa699e80e7f3399b2a65e024c9bcf445ad2", "sha256_in_prefix": "f2571d3da3c25523794205afb7253fa699e80e7f3399b2a65e024c9bcf445ad2", "size_in_bytes": 1307}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-312.pyc", "path_type": "hardlink", "sha256": "72fbc531c3cdab26f9c4acbb9d6e9861eed37931557447ab570c7a56e8ac3686", "sha256_in_prefix": "72fbc531c3cdab26f9c4acbb9d6e9861eed37931557447ab570c7a56e8ac3686", "size_in_bytes": 763}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-312.pyc", "path_type": "hardlink", "sha256": "1e97ad004c1cec50a1d4516e0d803e69a34f1e700b8024d6c5f8158dbf246afa", "sha256_in_prefix": "1e97ad004c1cec50a1d4516e0d803e69a34f1e700b8024d6c5f8158dbf246afa", "size_in_bytes": 4403}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-312.pyc", "path_type": "hardlink", "sha256": "e3112a36a4e4e57382428d19c1c1b644b1d55bca773ce09f7ab93dd4af407391", "sha256_in_prefix": "e3112a36a4e4e57382428d19c1c1b644b1d55bca773ce09f7ab93dd4af407391", "size_in_bytes": 4091}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "49984d7e15b4e5a423b721904c23c2bfe22151fb49a298bd59183720da7facad", "sha256_in_prefix": "49984d7e15b4e5a423b721904c23c2bfe22151fb49a298bd59183720da7facad", "size_in_bytes": 19116}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-312.pyc", "path_type": "hardlink", "sha256": "a08904961ac639b574794b947cc40050ab247cc400e9929cbc643059f6734a6a", "sha256_in_prefix": "a08904961ac639b574794b947cc40050ab247cc400e9929cbc643059f6734a6a", "size_in_bytes": 10369}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-312.pyc", "path_type": "hardlink", "sha256": "b5eaf36d8d200c0d519b2fa79bf5af6291161cf2401cc3fbe9c43b5f78666adf", "sha256_in_prefix": "b5eaf36d8d200c0d519b2fa79bf5af6291161cf2401cc3fbe9c43b5f78666adf", "size_in_bytes": 479}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-312.pyc", "path_type": "hardlink", "sha256": "e35012410724ed4aec5daa6e18c5610f1810ca03e99545d6a2e373371c02297f", "sha256_in_prefix": "e35012410724ed4aec5daa6e18c5610f1810ca03e99545d6a2e373371c02297f", "size_in_bytes": 395}, {"_path": "Lib/site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "bbd4efdd76b9ce76e770e5e4f5180d8cf7a4c51fb31739be1f5380fdd41f8bfd", "sha256_in_prefix": "bbd4efdd76b9ce76e770e5e4f5180d8cf7a4c51fb31739be1f5380fdd41f8bfd", "size_in_bytes": 150}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-312.pyc", "path_type": "hardlink", "sha256": "7c33fae636bc5d559790689554fe466e8f398f0aa6e22ad47843be273673dafd", "sha256_in_prefix": "7c33fae636bc5d559790689554fe466e8f398f0aa6e22ad47843be273673dafd", "size_in_bytes": 317}, {"_path": "Lib/site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "Lib/site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "11ebe5d3b166d8cb4f2e8bbcae93698e04db164463286583d7aaed22697c7af2", "sha256_in_prefix": "11ebe5d3b166d8cb4f2e8bbcae93698e04db164463286583d7aaed22697c7af2", "size_in_bytes": 150}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-312.pyc", "path_type": "hardlink", "sha256": "6fce3ac50653cfb6e27bd7ff875683475a69b0b7c98fbfab33a74382e727217c", "sha256_in_prefix": "6fce3ac50653cfb6e27bd7ff875683475a69b0b7c98fbfab33a74382e727217c", "size_in_bytes": 40016}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-312.pyc", "path_type": "hardlink", "sha256": "3fd901cf124db1f116e2747a0dd7d8f6cdc061501ff3687a21395b6d00171db5", "sha256_in_prefix": "3fd901cf124db1f116e2747a0dd7d8f6cdc061501ff3687a21395b6d00171db5", "size_in_bytes": 11617}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-312.pyc", "path_type": "hardlink", "sha256": "e26cdfe6900e9cbe003f4c4e86cd4b10bff3e3419a403192c7dffa61c673d024", "sha256_in_prefix": "e26cdfe6900e9cbe003f4c4e86cd4b10bff3e3419a403192c7dffa61c673d024", "size_in_bytes": 15756}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-312.pyc", "path_type": "hardlink", "sha256": "705d8eb2a9431721a193d9daa09c2444278e5271754f0effb0562794ab3d7b38", "sha256_in_prefix": "705d8eb2a9431721a193d9daa09c2444278e5271754f0effb0562794ab3d7b38", "size_in_bytes": 4199}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-312.pyc", "path_type": "hardlink", "sha256": "88c8e4bf194caa3e0df8db0a821b7d9f8c89f05da9f3c898ab76859b2a58d463", "sha256_in_prefix": "88c8e4bf194caa3e0df8db0a821b7d9f8c89f05da9f3c898ab76859b2a58d463", "size_in_bytes": 43018}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "sha256_in_prefix": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "size_in_bytes": 1827}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "d4893f993fa019bafb123c9d1fc54e5a1c68b0dcb21ba3c0d6b6e24585030deb", "sha256_in_prefix": "d4893f993fa019bafb123c9d1fc54e5a1c68b0dcb21ba3c0d6b6e24585030deb", "size_in_bytes": 3062}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-312.pyc", "path_type": "hardlink", "sha256": "f95ba85390eca55a717c7edb8c3d0d214a3506f8017d121f9d0a539f3a42f44c", "sha256_in_prefix": "f95ba85390eca55a717c7edb8c3d0d214a3506f8017d121f9d0a539f3a42f44c", "size_in_bytes": 748}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "sha256_in_prefix": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "size_in_bytes": 28807}, {"_path": "Lib/site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "sha256_in_prefix": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "size_in_bytes": 8933}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "sha256_in_prefix": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "size_in_bytes": 12406}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "sha256_in_prefix": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "size_in_bytes": 3271}, {"_path": "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "sha256_in_prefix": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "size_in_bytes": 33427}, {"_path": "Lib/site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "Lib/site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "Lib/site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "Lib/site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "ccd25a0c2fc63d5c526f9f405b6a429ebe59068a3f793b839b91aa060879b097", "sha256_in_prefix": "ccd25a0c2fc63d5c526f9f405b6a429ebe59068a3f793b839b91aa060879b097", "size_in_bytes": 155}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-312.pyc", "path_type": "hardlink", "sha256": "91e8afeee255b29cdae5cb8e778d15eceeedf6f4d8caa62308e3d56183351509", "sha256_in_prefix": "91e8afeee255b29cdae5cb8e778d15eceeedf6f4d8caa62308e3d56183351509", "size_in_bytes": 4588}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-312.pyc", "path_type": "hardlink", "sha256": "9f086082a8b867744aab64b74879c660edb39dd122452776dd060e20cccafcf2", "sha256_in_prefix": "9f086082a8b867744aab64b74879c660edb39dd122452776dd060e20cccafcf2", "size_in_bytes": 9068}, {"_path": "Lib/site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "sha256_in_prefix": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "size_in_bytes": 8256}, {"_path": "Lib/site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "Lib/site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "Lib/site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "sha256_in_prefix": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "size_in_bytes": 2397}, {"_path": "Lib/site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "sha256_in_prefix": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "size_in_bytes": 1957}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "sha256_in_prefix": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "size_in_bytes": 23083}, {"_path": "Lib/site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "Lib/site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "Lib/site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "sha256_in_prefix": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "size_in_bytes": 10099}, {"_path": "Lib/site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "sha256_in_prefix": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "size_in_bytes": 34118}, {"_path": "Lib/site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "sha256_in_prefix": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "size_in_bytes": 14186}, {"_path": "Lib/site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "sha256_in_prefix": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "size_in_bytes": 22580}, {"_path": "Lib/site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "sha256_in_prefix": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "size_in_bytes": 20881}, {"_path": "Lib/site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "Lib/site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "Lib/site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "sha256_in_prefix": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "size_in_bytes": 8901}, {"_path": "Lib/site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "sha256_in_prefix": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "size_in_bytes": 7077}, {"_path": "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "sha256_in_prefix": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "size_in_bytes": 5987}, {"_path": "Lib/site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "sha256_in_prefix": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "size_in_bytes": 53534}, {"_path": "Lib/site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "sha256_in_prefix": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "size_in_bytes": 43383}, {"_path": "Lib/site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "sha256_in_prefix": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "size_in_bytes": 44866}, {"_path": "Lib/site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "Lib/site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "sha256_in_prefix": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "size_in_bytes": 887}, {"_path": "Lib/site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "sha256_in_prefix": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "size_in_bytes": 3433}, {"_path": "Lib/site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "sha256_in_prefix": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "size_in_bytes": 2099}, {"_path": "Lib/site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "sha256_in_prefix": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "size_in_bytes": 18562}, {"_path": "Lib/site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "Lib/site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "sha256_in_prefix": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "size_in_bytes": 8775}, {"_path": "Lib/site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "sha256_in_prefix": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "size_in_bytes": 4330}, {"_path": "Lib/site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "sha256_in_prefix": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "size_in_bytes": 32872}, {"_path": "Lib/site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "Lib/site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "sha256_in_prefix": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "size_in_bytes": 9008}, {"_path": "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "path_type": "hardlink", "sha256": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "sha256_in_prefix": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "Lib/site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "Lib/site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "Lib/site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "sha256_in_prefix": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "size_in_bytes": 19370}, {"_path": "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "sha256_in_prefix": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "size_in_bytes": 7881}, {"_path": "Lib/site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "Lib/site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "Lib/site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "sha256_in_prefix": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "size_in_bytes": 3189}, {"_path": "Lib/site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "sha256_in_prefix": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "size_in_bytes": 3796}, {"_path": "Lib/site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "sha256_in_prefix": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "size_in_bytes": 8624}, {"_path": "Lib/site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}], "paths_version": 1}, "requested_spec": "None", "sha256": "4821c198bcea0d099bf0032a7a2f304b6477d1e6f6c51cb93653661759761233", "size": 2320341, "subdir": "win-64", "timestamp": 1746025001000, "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/win-64/setuptools-78.1.1-py312haa95532_0.conda", "version": "78.1.1"}