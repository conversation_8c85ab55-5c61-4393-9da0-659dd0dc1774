from flask import Blueprint, request, jsonify, make_response
from flask import current_app

from spiffworkflow_backend.models.user import UserModel
from spiffworkflow_backend.models.process_instance import ProcessInstanceModel
from spiffworkflow_backend.models.human_task import HumanTaskModel
from spiffworkflow_backend.models.human_task_user import HumanTask<PERSON>serModel
from spiffworkflow_backend.services.authorization_service import AuthorizationService
from spiffworkflow_backend.services.user_service import UserService
from spiffworkflow_backend.services.sdk_message_sent_service import SMSService
from spiffworkflow_backend.services.sms_template_service import SmsTemplateService
from spiffworkflow_backend.exceptions.api_error import ApiError
from spiffworkflow_backend.models.db import db

reminder_blueprint = Blueprint("reminder_blueprint", __name__, url_prefix="/v1.0/reminder")


@reminder_blueprint.route("/send-process-reminder", methods=["POST"])
def send_process_reminder():
    """
    发送流程催办短信
    
    请求体:
    {
        "process_instance_id": 123,
        "message": "请及时处理您的待办任务"  // 可选，自定义催办消息
    }
    """
    try:
        # 权限检查
        current_user = UserService.current_user()
        if not AuthorizationService.user_has_permission(
            user=current_user,
            permission="create",
            target_uri="/reminder/send-process-reminder"
        ):
            raise ApiError("permission_denied", "You don't have permission to send reminders")

        # 获取请求参数
        body = request.get_json()
        if not body:
            raise ApiError("invalid_request", "Request body is required")

        process_instance_id = body.get("process_instance_id")
        custom_message = body.get("message", "")

        if not process_instance_id:
            raise ApiError("missing_process_instance_id", "process_instance_id is required")

        # 获取流程实例
        process_instance = ProcessInstanceModel.query.filter_by(id=process_instance_id).first()
        if not process_instance:
            raise ApiError("process_instance_not_found", f"Process instance {process_instance_id} not found")

        # 检查权限：只有流程发起人可以催办
        if process_instance.process_initiator_id != current_user.id:
            raise ApiError("permission_denied", "Only process initiator can send reminders")

        # 获取当前待办任务和处理人
        active_tasks = HumanTaskModel.query.filter_by(
            process_instance_id=process_instance_id,
            completed=False
        ).all()

        if not active_tasks:
            raise ApiError("no_active_tasks", "No active tasks found for this process instance")

        # 收集所有待办任务处理人
        assignees = set()
        for task in active_tasks:
            # 获取分配给该任务的用户
            task_users = HumanTaskUserModel.query.filter_by(human_task_id=task.id).all()
            for task_user in task_users:
                assignees.add(task_user.user_id)

        if not assignees:
            raise ApiError("no_assignees", "No assignees found for active tasks")

        # 发送催办短信给每个处理人
        sms_service = SMSService()
        results = []
        
        for user_id in assignees:
            user = UserModel.query.filter_by(id=user_id).first()
            if not user:
                continue

            # 获取用户手机号（可能存储在不同字段中）
            phone = None
            if hasattr(user, 'phone') and user.phone:
                phone = user.phone
            elif hasattr(user, 'phone_number') and user.phone_number:
                phone = user.phone_number

            if not phone:
                results.append({
                    "user_id": user_id,
                    "username": user.username,
                    "phone": None,
                    "success": False,
                    "error": "User has no phone number"
                })
                continue
                
            # 构建催办消息
            if custom_message:
                message_content = custom_message
            else:
                message_content = f"您有待办任务需要处理，流程：{process_instance.process_model_display_name}，请及时处理。"
            
            # 发送短信
            try:
                result = sms_service.send_sms_with_template(
                    phone_numbers=phone,
                    sign_name="阿里云短信测试",
                    template_code="SMS_REMINDER",  # 催办模板
                    template_params={"content": message_content},
                    user_id=current_user.id,
                    process_instance_id=process_instance_id
                )

                results.append({
                    "user_id": user_id,
                    "username": user.username,
                    "phone": phone,
                    "success": result["success"],
                    "message_instance_id": result.get("message_instance_id"),
                    "error": result.get("error")
                })

            except Exception as e:
                results.append({
                    "user_id": user_id,
                    "username": user.username,
                    "phone": phone,
                    "success": False,
                    "error": str(e)
                })

        return make_response(jsonify({
            "success": True,
            "process_instance_id": process_instance_id,
            "process_name": process_instance.process_model_display_name,
            "reminder_results": results,
            "total_sent": len([r for r in results if r["success"]]),
            "total_failed": len([r for r in results if not r["success"]])
        }), 200)

    except ApiError:
        raise
    except Exception as e:
        raise ApiError("send_reminder_failed", f"Failed to send reminder: {str(e)}")


@reminder_blueprint.route("/process-assignees/<int:process_instance_id>", methods=["GET"])
def get_process_assignees(process_instance_id: int):
    """
    获取流程当前待办任务的处理人信息
    """
    try:
        # 权限检查
        current_user = UserService.current_user()
        
        # 获取流程实例
        process_instance = ProcessInstanceModel.query.filter_by(id=process_instance_id).first()
        if not process_instance:
            raise ApiError("process_instance_not_found", f"Process instance {process_instance_id} not found")

        # 检查权限：只有流程发起人可以查看
        if process_instance.process_initiator_id != current_user.id:
            raise ApiError("permission_denied", "Only process initiator can view assignees")

        # 获取当前待办任务
        active_tasks = HumanTaskModel.query.filter_by(
            process_instance_id=process_instance_id,
            completed=False
        ).all()

        assignees_info = []
        for task in active_tasks:
            task_info = {
                "task_id": task.id,
                "task_name": task.task_name,
                "task_title": task.task_title,
                "assignees": []
            }

            # 获取分配给该任务的用户
            task_users = HumanTaskUserModel.query.filter_by(human_task_id=task.id).all()
            for task_user in task_users:
                user = UserModel.query.filter_by(id=task_user.user_id).first()
                if user:
                    phone = None
                    if hasattr(user, 'phone') and user.phone:
                        phone = user.phone
                    elif hasattr(user, 'phone_number') and user.phone_number:
                        phone = user.phone_number

                    task_info["assignees"].append({
                        "user_id": user.id,
                        "username": user.username,
                        "display_name": getattr(user, 'display_name', user.username),
                        "phone": phone,
                        "type": "assigned_user"
                    })

            assignees_info.append(task_info)

        return make_response(jsonify({
            "process_instance_id": process_instance_id,
            "process_name": process_instance.process_model_display_name,
            "tasks": assignees_info
        }), 200)

    except ApiError:
        raise
    except Exception as e:
        raise ApiError("get_assignees_failed", f"Failed to get assignees: {str(e)}")
