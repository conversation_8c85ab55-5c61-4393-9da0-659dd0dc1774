import json
from typing import Any

import flask.wrappers
from flask import current_app
from flask import jsonify
from flask import make_response
from flask.wrappers import Response

from spiffworkflow_backend.models.db import db
from spiffworkflow_backend.models.message_instance import MessageInstanceModel
from spiffworkflow_backend.models.message_model import MessageCorrelationPropertyModel
from spiffworkflow_backend.models.message_model import MessageModel
from spiffworkflow_backend.models.process_instance import ProcessInstanceModel
from spiffworkflow_backend.models.process_instance import ProcessInstanceModelSchema
from spiffworkflow_backend.services.message_service import MessageService
from spiffworkflow_backend.services.upsearch_service import UpsearchService


def message_model_list_from_root() -> flask.wrappers.Response:
    return message_model_list()


def message_model_list(relative_location: str | None = None) -> flask.wrappers.Response:
    # Returns all the messages and correlation properties that exist at the given
    # relative location or higher in the directory tree.

    loc = relative_location.replace(":", "/") if relative_location else ""
    locations = UpsearchService.upsearch_locations(loc)
    messages = db.session.query(MessageModel).filter(MessageModel.location.in_(locations)).order_by(MessageModel.identifier).all()  # type: ignore

    def correlation_property_response(correlation_property: MessageCorrelationPropertyModel) -> dict[str, str]:
        return {
            "identifier": correlation_property.identifier,
            "retrieval_expression": correlation_property.retrieval_expression,
        }

    def message_response(message: MessageModel) -> dict[str, Any]:
        return {
            "identifier": message.identifier,
            "location": message.location,
            "schema": message.schema,
            "correlation_properties": [correlation_property_response(cp) for cp in message.correlation_properties],
        }

    return make_response(jsonify({"messages": [message_response(m) for m in messages]}), 200)


def message_instance_list(
    process_instance_id: int | None = None,
    page: int = 1,
    per_page: int = 100,
    message_category: str | None = None,
    search: str | None = None,
    status: str | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
) -> flask.wrappers.Response:


    # to make sure the process instance exists
    message_instances_query = MessageInstanceModel.query

    if process_instance_id:
        message_instances_query = message_instances_query.filter_by(process_instance_id=process_instance_id)

    # 按消息类型筛选
    if message_category:
        try:
            category_int = int(message_category)
            print(f"DEBUG: Filtering by message_category: {category_int}")
            # 使用 filter 而不是 filter_by 来处理可能的 NULL 值
            message_instances_query = message_instances_query.filter(
                MessageInstanceModel.message_category == category_int
            )
        except ValueError:
            print(f"DEBUG: Invalid message_category value: {message_category}")
            # 如果传入的不是数字，忽略筛选
            pass

    # 模糊查询：支持按接收人、消息名称、payload内容搜索
    if search:
        search_pattern = f"%{search}%"
        print(f"DEBUG: Searching with pattern: {search_pattern}")
        message_instances_query = message_instances_query.filter(
            db.or_(
                MessageInstanceModel.recipient.like(search_pattern),
                MessageInstanceModel.name.like(search_pattern),
                MessageInstanceModel.payload.like(search_pattern)
            )
        )

    # 按状态筛选
    if status:
        print(f"DEBUG: Filtering by status: {status}")
        message_instances_query = message_instances_query.filter(
            MessageInstanceModel.status == status
        )

    # 按时间范围筛选
    if start_date:
        try:
            from datetime import datetime
            start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp())
            print(f"DEBUG: Filtering from date: {start_date} (timestamp: {start_timestamp})")
            message_instances_query = message_instances_query.filter(
                MessageInstanceModel.created_at_in_seconds >= start_timestamp
            )
        except ValueError:
            print(f"DEBUG: Invalid start_date format: {start_date}")

    if end_date:
        try:
            from datetime import datetime
            # 结束日期加1天，表示当天结束
            end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp()) + 86400
            print(f"DEBUG: Filtering to date: {end_date} (timestamp: {end_timestamp})")
            message_instances_query = message_instances_query.filter(
                MessageInstanceModel.created_at_in_seconds < end_timestamp
            )
        except ValueError:
            print(f"DEBUG: Invalid end_date format: {end_date}")

    message_instances = (
        message_instances_query.order_by(
            MessageInstanceModel.created_at_in_seconds.desc(),  # type: ignore
            MessageInstanceModel.id.desc(),  # type: ignore
        )
        .outerjoin(ProcessInstanceModel)  # Not all messages were created by a process
        .add_columns(
            ProcessInstanceModel.process_model_identifier,
            ProcessInstanceModel.process_model_display_name,
        )
        .paginate(page=page, per_page=per_page, error_out=False)
    )

    response_json = {
        "results": message_instances.items,
        "pagination": {
            "count": len(message_instances.items),
            "total": message_instances.total,
            "pages": message_instances.pages,
        },
    }

    # 添加调试信息
    print(f"DEBUG: Total results before pagination: {message_instances.total}")
    print(f"DEBUG: Applied filters - category: {message_category}, search: {search}, status: {status}, start_date: {start_date}, end_date: {end_date}")

    # 检查前几条记录的字段值
    for item in message_instances.items[:3]:
        # 因为使用了 outerjoin，item 是一个 tuple，第一个元素是 MessageInstanceModel
        if isinstance(item, tuple) and len(item) > 0:
            msg = item[0]  # MessageInstanceModel 实例
            print(f"DEBUG: Record ID {msg.id}, category: {msg.message_category}, recipient: {msg.recipient}, status: {msg.status}")
        elif hasattr(item, 'id'):
            print(f"DEBUG: Record ID {item.id}, category: {item.message_category}, recipient: {item.recipient}, status: {item.status}")

    return make_response(jsonify(response_json), 200)


# body: {
#   payload: dict,
#   process_instance_id: Optional[int],
# }
#
# For example:
# curl 'http://localhost:7000/v1.0/messages/gogo' \
#  -H 'authorization: Bearer [FIXME]' \
#  -H 'content-type: application/json' \
#  --data-raw '{"payload":{"sure": "yes", "food": "spicy"}}'
def message_send(
    modified_message_name: str,
    body: dict[str, Any],
    execution_mode: str | None = None,
) -> flask.wrappers.Response:
    receiver_message = MessageService.run_process_model_from_message(modified_message_name, body, execution_mode)
    process_instance = ProcessInstanceModel.query.filter_by(id=receiver_message.process_instance_id).first()
    response_json = {
        "task_data": process_instance.get_data(),
        "process_instance": ProcessInstanceModelSchema().dump(process_instance),
    }
    return Response(
        json.dumps(response_json),
        status=200,
        mimetype="application/json",
    )
