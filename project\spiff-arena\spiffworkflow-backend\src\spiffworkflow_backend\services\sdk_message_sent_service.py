import os
import time
import json
from typing import Dict, Any, Optional

from alibabacloud_dysmsapi20170525.client import Client as DysmsClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysms_models
from alibabacloud_tea_util import models as util_models

from spiffworkflow_backend.models.db import db
from spiffworkflow_backend.models.message_instance import MessageInstanceModel
from spiffworkflow_backend.services.sms_template_service import SmsTemplateService
from spiffworkflow_backend.exceptions.api_error import ApiError

class SMSService:
    def __init__(self):
        self.client = self.create_client()
        print("创建DysmsClient实例成功。")
    
    def create_client(self) -> DysmsClient:
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        config = open_api_models.Config(
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。,
            access_key_id=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'],
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。,
            access_key_secret=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']
        )
        config.endpoint = f'dysmsapi.aliyuncs.com'
        return DysmsClient(config)
    
    def send_sms(self, phone_numbers: str, sign_name: str, template_code: str, template_param: str = None) -> object:
        """
        发送短信服务
        :param phone_numbers: 接收短信的手机号码
        :param sign_name: 短信签名
        :param template_code: 短信模板code
        :param template_param: 模板参数,JSON格式字符串
        :return: 发送结果
        """
        send_sms_request = dysms_models.SendSmsRequest(
            phone_numbers=phone_numbers,
            sign_name=sign_name,
            template_code=template_code,
            template_param=template_param
        )

        # 创建运行时参数
        runtime = util_models.RuntimeOptions()

        # 发起请求并返回结果
        return self.client.send_sms_with_options(send_sms_request, runtime)

    def send_sms_with_template(self, phone_numbers: str, sign_name: str,
                              template_code: str, template_params: dict = None,
                              user_id: int = None, process_instance_id: int = None) -> dict:
        """
        使用模板发送短信并记录到消息实例

        :param phone_numbers: 接收短信的手机号码
        :param sign_name: 短信签名
        :param template_code: 短信模板代码
        :param template_params: 模板参数字典，如 {"code": "123456"}
        :param user_id: 发送用户ID
        :param process_instance_id: 流程实例ID（可选）
        :return: 发送结果和消息实例ID
        """
        try:
            # 1. 渲染短信模板
            if template_params is None:
                template_params = {}

            final_content = SmsTemplateService.render_template(template_code, template_params)
            print(f"Sending SMS with template code {template_code} and params {template_params} 成功获取模板")
            # 2. 发送短信
            template_param_json = json.dumps(template_params) if template_params else None
            response = self.send_sms(phone_numbers, sign_name, template_code, template_param_json)

            # 3. 判断发送结果
            success = hasattr(response, 'body') and hasattr(response.body, 'code') and response.body.code == 'OK'
            status = "completed" if success else "failed"
            failure_cause = None if success else getattr(response.body, 'message', 'Unknown error')
           
            # 4. 创建消息实例记录
            now = int(time.time())
            message_instance = MessageInstanceModel(
                process_instance_id=process_instance_id,
                name=f"SMS_{template_code}",
                message_type="send",
                message_category=2,  # 2 = 短信
                recipient=phone_numbers,
                payload={"content": final_content, "template_code": template_code, "template_params": template_params},
                correlation_keys={},
                status=status,
                user_id=user_id,
                failure_cause=failure_cause,
                created_at_in_seconds=now,
                updated_at_in_seconds=now
            )

            db.session.add(message_instance)
            db.session.commit()

            return {
                "success": success,
                "message_instance_id": message_instance.id,
                "content": final_content,
                "response": response,
                "error": failure_cause
            }

        except Exception as e:
            # 发送失败也要记录
            now = int(time.time())
            message_instance = MessageInstanceModel(
                process_instance_id=process_instance_id,
                name=f"SMS_{template_code}",
                message_type="send",
                message_category=2,  # 2 = 短信
                recipient=phone_numbers,
                payload={"template_code": template_code, "template_params": template_params or {}},
                correlation_keys={},
                status="failed",
                user_id=user_id,
                failure_cause=str(e),
                created_at_in_seconds=now,
                updated_at_in_seconds=now
            )

            db.session.add(message_instance)
            db.session.commit()

            return {
                "success": False,
                "message_instance_id": message_instance.id,
                "content": None,
                "response": None,
                "error": str(e)
            }


# 示例用法
# if __name__ == '__main__':
#     # 创建短信服务实例
#     sms_service = SMSService()
    
#     # 发送短信
#     response = sms_service.send_sms(
#         phone_numbers='18258843019',
#         sign_name='阿里云短信测试',
#         template_code='SMS_154950909',
#         template_param='{"code":"201314"}'
#     )
    
#     print(response)