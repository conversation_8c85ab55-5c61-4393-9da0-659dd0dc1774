{"server_error_title": "服务器错误", "server_error_message": "很抱歉给您带来不便，由于技术问题，我们的服务暂时不可用。请稍候，我们正在解决问题。若问题持续存在，请联系网站管理员。", "home": "首页", "processes": "流程管理", "process_instances": "流程实例", "data_stores": "数据存储", "messages": "消息中心", "configuration": "系统配置", "attachment_management": "附件管理", "lab_files": "实验室文件", "lab_file_management": "实验室文件管理", "expand_navigation": "展开导航栏", "collapse_navigation": "折叠导航栏", "user_actions": "用户操作", "toggle_dark_mode": "切换暗黑模式", "environment": "运行环境", "sign_out": "退出登录", "about": "关于系统", "documentation": "文档中心", "tasks_assigned_to_me": "我的待办任务", "workflows_created_by_me": "我创建的流程", "group_by": "分组方式", "ungrouped": "取消分组", "toggle_table_tiles": "切换表格/卡片视图", "show_non_active": "显示非活跃项", "id": "唯一标识", "task_details": "任务详情", "created": "创建时间", "last_milestone": "最后里程碑", "last_updated": "最后更新时间", "waiting_for": "当前处理人", "actions": "操作菜单", "complete_task": "完成任务", "login_title": "欢迎登录", "homepage_title": "主页", "homepage_title_tile": "主页(移动端)", "english": "英文", "chinese": "中文", "language_settings": "语言设置", "select_language": "选择语言", "responsible_party": "负责方", "process_group": "流程组", "tasks_for": "任务分配给", "tasks_from_process_group": "来自流程组的任务", "tasks_for_user_group": "分配给用户组的任务", "me": "我", "last_process_instance": "最后的流程实例", "process_models": "流程模型", "process_groups": "流程组", "favorites": "收藏夹", "searching_for": "搜索", "all": "全部", "add": "添加", "edit": "编辑", "delete": "删除", "delete_process_model": "删除流程模型", "delete_process_group": "删除流程组", "edit_process_group": "编辑流程组", "add_process_model": "添加流程模型", "add_process_group": "添加流程组", "add_data_store": "添加数据存储", "search_placeholder": "搜索流程...", "no_results_found": "未找到结果", "primary_file": "主文件", "root": "根目录", "choose_a_process": "选择一个流程", "choose_a_process_model": "选择一个流程模型", "start_this_process": "开始这个流程", "new_process_group": "新建流程组", "display_name": "显示名称", "display_name_required": "显示名称为必填项。", "identifier": "标识符", "identifier_required": "标识符为必填项，且只有小写字母和连字符'-'组成，如:add-user", "description": "描述", "submit": "提交", "submitText": "提交内容", "continue": "继续", "editing": "编辑", "edit_process_group_title": "编辑流程组", "add_new_process_model": "添加新流程模型", "add_process_model_title": "添加流程模型", "edit_process_model_title": "编辑流程模型", "process_model": "流程模型", "frontend_version_information": "前端版本信息", "backend_version_information": "后端版本信息", "secrets": "密钥", "authentications": "认证", "process_instance_perspectives": "流程实例视角", "choose_process_instance_perspective": "选择一个流程实例视角", "choose_status": "选择状态", "status": "状态", "started_by": "发起人", "start_typing_username": "开始输入用户名", "enter_username": "输入用户名", "start_date_from": "开始日期从", "start_date_to": "开始日期到", "end_date_from": "结束日期从", "end_date_to": "结束日期到", "clear": "清除", "advanced": "高级", "save": "保存", "add_column": "添加列", "edit_column": "编辑 {{column}}列", "remove_column": "移除列", "column_options": "列选项", "choose_a_column_to_show": "选择要显示的列", "column": "列", "display_type": "显示类型", "operator": "操作符", "condition_value": "条件值", "advanced_filter_options": "高级筛选选项", "system_report": "系统报告", "perspective_updated": "视角：已更新", "perspective_created": "视角：已创建", "my_process_instances": "我的流程实例", "select_time": "选择时间", "process_instance_id": "流程实例ID", "process_instance_id_must_be_number": "流程实例ID必须是数字。", "all_process_instances": "全部流程实例", "created_by": "创建者", "save_as_perspective": "另存为视角", "save_perspective": "保存视角", "cancel": "取消", "save_perspective_description_new": "将当前的列和筛选器保存为视角，以便将来可以回到此视图。", "save_perspective_description_edit": "保持标识符不变并点击保存以更新当前视角。如果要以新名称保存当前视图，请更改标识符。", "for_me": "我的", "find_by_id": "按ID查找", "list_of_tabs": "标签页列表", "tooltip_for_me": "仅显示当前用户的流程实例", "tooltip_all": "显示所有用户的流程实例", "tooltip_find_by_id": "按ID搜索流程实例", "identifier_validation": "标识符是必填项，且必须是小写字母和连字符。", "notification_type": "通知类型", "fault": "故障", "notification_addresses": "通知地址", "notification_addresses_description": "您可以提供一个或多个地址，以便在此模型失败时接收通知。", "add_notification_address": "添加通知地址", "metadata_extractions": "元数据提取", "metadata_extractions_description": "您可以提供一个或多个元数据提取，从流程实例中提取数据，以便在搜索和视角中快速访问。", "add_metadata_extraction_path": "添加元数据提取路径", "extraction_key": "提取键", "extraction_path": "提取路径", "remove_key": "移除键", "address": "地址", "remove_address": "移除地址", "name": "名称", "name_required": "名称是必填项。", "type": "类型", "schema": "模式", "schema_required": "模式是必填项且必须是有效的JSON。", "files": "文件", "add_file": "添加文件", "upload_file": "上传文件", "new_bpmn_file": "新建BPMN文件", "new_dmn_file": "新建DMN文件", "new_json_file": "新建JSON文件", "new_markdown_file": "新建Markdown文件", "no_files_message": "**此流程模型没有关联任何文件。请尝试在下方下拉菜单中选择'新建BPMN文件'来创建bpmn文件。**", "revision": "版本", "upload_file_title": "上传文件", "upload_file_description": "最大文件大小为500MB。仅支持.bpmn、.dmn、.json和.md文件。", "delete_file": "删除文件", "download_file": "下载文件", "set_as_primary_file": "设为主文件", "run_bpmn_unit_tests": "运行此文件中定义的BPMN单元测试", "could_not_find_file_contents": "无法找到文件内容：{{fileName}}", "delete_file_confirmation": "删除文件：{{fileName}}", "file": "文件", "upload": "上传", "overwrite_file_title": "覆盖文件？", "overwrite_file_message": "覆盖文件：{{filename}}", "yes": "是", "process_model_file_name": "流程模型文件名", "file_name": "文件名", "save_changes": "保存更改", "file_saved": "文件已保存：", "changes_to_file_saved": "文件更改已保存。", "process_model_file": "流程模型文件", "view_diagram": "查看图表", "editing_script": "编辑脚本", "script_editor": "脚本编辑器", "script_assist": "脚本助手", "unit_tests": "单元测试", "close": "关闭", "edit_markdown": "编辑Markdown", "message_editor": "消息编辑器", "create_edit_message": "创建或编辑消息并管理其关联属性", "close_does_not_save": "关闭（不保存）", "select_process_model": "选择流程模型", "process_model_search": "流程模型搜索", "edit_json_schema": "编辑JSON模式", "process_model_file_name_required": "流程模型文件名是必填项。", "unsaved_changes": "未保存的更改。", "please_save_to_avoid_losing_work": "请保存以避免丢失您的工作。", "unit_test": "单元测试", "input_json": "输入JSON", "expected_output_json": "预期输出JSON", "unexpected_result_comparison": "意外结果。请查看下方的预期/实际对比。", "error_encountered_line": "运行脚本时遇到错误。请检查第{{line}}行附近的代码", "error_encountered_script": "运行脚本时遇到错误。{{error}}", "json_formatting_error": "提供的JSON包含格式错误。", "ask_spiff_ai": "询问Spiff AI", "create_python_script": "创建一个Python脚本...", "use_natural_language_hint": "使用自然语言创建您的脚本。提示：从基础开始，然后编辑调整。", "edit_readme": "编辑README.md", "no_readme_file_found": "未找到README文件", "add_readme": "添加README.md", "updated": "更新时间", "completed": "完成时间", "current_diagram": "当前图表", "started": "开始事件", "terminate": "终止", "suspend": "暂停", "migrate": "迁移", "resume": "恢复", "copy_shareable_short_link": "复制可分享的短链接", "terminate_process_instance": "终止流程实例：{{id}}", "delete_process_instance": "删除流程实例：{{id}}", "value": "值", "data_object": "数据对象", "create_script_unit_test": "创建脚本单元测试", "view_call_activity_diagram": "查看调用活动图表", "edit_task_data": "编辑任务数据", "assign_user": "分配用户", "execute_task": "执行任务", "skip_task": "跳过任务", "send_event": "发送事件", "reset_process_here": "在此重置流程", "task_data": "任务数据", "edit_task_data_header": "编辑任务数据", "update_task_ownership": "更新任务所有权", "select_user_to_complete_task": "选择允许完成此任务的用户", "choose_event_to_send": "选择要发送的事件", "select_event_message": "选择要发送的事件。消息事件还需要正文。", "task_instances": "任务实例", "completed_instances": "MI任务的已完成实例", "running_instances": "MI任务的运行中实例", "future_instances": "MI任务的未来实例", "loop_iterations": "循环迭代", "remaining": "剩余", "send": "发送", "view": "查看", "link": "链接", "copy_shareable_link": "复制可分享链接", "filter_options": "筛选选项", "copied_link_to_clipboard": "链接已复制到剪贴板", "process": "流程", "process_instance": "流程实例", "corresponding_message_instance": "对应消息实例", "details": "详情", "created_at": "创建时间", "external_call": "外部调用", "instance_has_error": "实例有错误", "message_data": "消息 {{id}} ({{name}}) {{messageType}} 数据：", "correlations": "关联：", "process_instance_label": "流程实例：{{id}}", "select_data_store": "选择数据存储", "secret_key": "密钥", "creator": "创建者", "no_secrets_to_display": "没有密钥可显示", "add_a_secret": "添加密钥", "diagram": "流程图", "milestones": "里程碑", "events": "事件", "tasks": "任务", "completed_by_me": "我完成的", "all_completed": "全部已完成", "return_to_multiinstance_task": "返回到多实例任务", "return_to_loop_task": "返回到循环任务", "viewing_process_instance_at_time": "正在查看流程实例在", "was_active": "处于活动状态时的状态。", "view_current_process_instance_state": "查看当前流程实例状态。", "failed_to_load_diagram": "加载流程图失败", "tasks_i_can_complete": "我可以完成的任务", "tasks_i_can_complete_description": "这些是您可以完成的任务，要么是因为它们被分配给了您所在的组，要么是因为它们直接分配给了您。", "no_tasks_to_complete": "此流程实例没有您可以完成的任务。", "no_completed_tasks_by_me": "您尚未完成此流程实例的任何任务。", "no_completed_tasks": "此流程实例没有已完成的任务。", "task_table_id": "ID", "task_table_process": "流程", "task_table_task": "任务", "task_table_started_by": "发起人", "task_table_waiting_for": "等待", "task_table_completed_by": "完成人", "task_table_date_started": "开始日期", "task_table_last_updated": "最后更新", "task_table_actions": "操作", "event_table_id": "ID", "event_table_bpmn_process": "BPMN流程", "event_table_task_name": "任务名称", "event_table_task_identifier": "任务标识符", "event_table_task_type": "任务类型", "event_table_event_type": "事件类型", "event_table_user": "用户", "event_table_timestamp": "时间戳", "milestone_table_task_name": "里程碑", "go": "执行", "items_per_page": "每页条数:", "pagination_of": "共", "assigned_user_group": "分配的用户组", "include_oldest_open_task_info": "包含最早未完成任务信息", "include_tasks_for_me": "包含我的任务", "start_date_from_after_start_date_to": "\"开始日期从\"不能晚于\"开始日期到\"", "end_date_from_after_end_date_to": "\"结束日期从\"不能晚于\"结束日期到\"", "start_date_from_after_end_date_from": "\"开始日期从\"不能晚于\"结束日期从\"", "start_date_to_after_end_date_to": "\"开始日期到\"不能晚于\"结束日期到\"", "edit_column_title": "编辑 {{column}} 列", "columns": "列", "are_you_sure": "您确定吗？", "ok": "确定", "sentry_link_description": ": 在此处查找有关此错误的详细信息（可能需要一段时间才能显示）: ", "context": "上下文", "call_activity_trace": "调用活动跟踪", "stacktrace": "堆栈跟踪", "error": "错误:", "refresh_data_in_table": "刷新表格数据", "view_filterable_list": "查看可筛选列表", "result": "结果", "page_not_found_title": "404 页面未找到", "page_not_found_message": "此页面不存在，请检查网址。", "both_report_metadata_and_additional_filters_provided": "同时提供了reportMetadata和additionalReportFilters。建议仅在使用reportIdentifier时使用additionalReportFilters,如果不使用reportIdentifier，请在reportMetadata中指定所有筛选器。", "both_report_identifier_and_metadata_provided": "同时提供了reportIdentifier和reportMetadata。您必须使用其中一个。", "terminated": "已终止", "suspended": "已暂停", "start": "开始时间", "end": "结束时间", "task": "任务", "task_assigned_to_different_person_or_team": "下一个任务已分配给其他人员或团队。", "task_assigned_to_group": "下一个任务已分配给组：{{groupId}}。", "task_assigned_to_users": "下一个任务已分配给用户：{{users}}。", "no_action_for_you_at_this_time": "您目前无需采取任何操作。", "process_status": "流程{{status}}", "process_instance_status_by_admin": "此流程实例已被管理员{{status}}。请联系管理员获取更多信息。", "process_instance_unexpected_error": "此流程实例遇到意外错误，无法继续。请联系管理员获取更多信息和后续步骤。", "process_error": "流程错误", "no_additional_instructions_for_process": "此流程没有其他说明或信息。", "event_error_details": "事件错误详情", "error_details": "错误详情", "error_retrieving_error_details": "检索错误详情时出错：{{errorMessage}}", "not_authorized_to_view_error_details": "您无权查看错误详情", "event_has_an_error": "事件发生错误", "task_identifier": "任务标识符", "event_type": "事件类型", "reset": "重置", "page_cleared": "页面已清除", "process_instance_unexpected_error_cannot_continue": "此流程实例遇到意外错误，无法继续。请联系管理员获取更多信息和后续步骤。", "redirecting": "正在重定向...", "task_ready_to_complete": "任务{{taskTitle}}'已准备好供您完成。", "no_additional_instructions_for_task": "此任务没有其他说明或信息。", "you_completed_this_task": "您已完成此任务", "guid": "唯一标识", "view_process_instance": "查看流程实例 {{processInstanceId}}", "task_tooltip": "任务ID: {{taskName}}，SpiffWorkflow任务GUID: {{taskGuid}}", "view_task": "查看任务", "migrate_to_newest": "迁移到最新版本", "confirm_migrate_instance": "确认迁移实例", "confirm_dangerous_migration_message": "您确定要继续进行这个可能有风险的流程实例迁移吗？", "checking_if_ready_to_migrate": "正在检查是否准备好迁移", "ready_to_migrate_to_newest_version": "准备迁移到最新的流程模型版本", "nothing_to_migrate": "无需迁移", "process_instance_already_newest_version": "流程实例已经关联到最新的流程模型版本。", "process_instance_not_migratable": "流程实例无法迁移", "migration_requirements_explanation": "如果目标流程模型版本没有更改任何已完成的任务，并且流程实例不是最新版本，则可以迁移流程实例。", "process_instance_migrated": "流程实例已迁移", "initial_git_revision": "初始Git版本", "target_git_revision": "目标Git版本", "run_migration_to_version": "运行另一个迁移以切换到模型版本：'{{version}}'", "revert": "回退", "previous_migrations": "之前的迁移", "process_instance_with_id": "流程实例：{{processInstanceId}}", "failed_to_render_form": "表单渲染失败。", "form_could_not_be_built_with_current_schema": "无法使用当前的模式、UI和数据构建表单。请尝试纠正问题后重试。", "try_again": "重试", "please_check_json_schema_for_errors": "请检查JSON模式是否有错误。", "please_check_ui_settings_for_errors": "请检查UI设置是否有错误。", "please_check_data_view_for_errors": "请检查数据视图是否有错误。", "failed_to_save_file": "保存文件失败：'{{fileName}}'。{{errorMessage}}", "schema_name": "模式名称", "please_provide_schema_name_description": "请为您即将创建的模式/Web表单提供一个名称...", "name_validation_requirements": "名称是必需的，必须至少三个字符，并且必须是全小写字符和连字符。", "changes_will_be_automatically_saved_to": "您在此处所做的更改将自动保存到：", "for_the_schema": "用于模式", "for_additional_ui_form_settings": "用于附加UI表单设置", "for_example_data_to_test_form": "用于测试表单的示例数据", "create_files": "创建文件", "json_schema": "JSON模式", "ui_settings": "UI设置", "data_view": "数据视图", "examples": "示例", "json_schema_description": "JSON模式描述了您要收集的数据结构，以及应该应用于每个字段的验证规则。", "read_more": "阅读更多", "ui_settings_description": "这些UI设置增强了JSON模式，指定了Web表单应该如何显示。", "learn_more": "了解更多", "data_view_description": "在右侧表单中输入的数据将以与任务数据中提供的相同方式显示在下方。为了在工作流中使用预配置值初始化表单或为动态下拉列表设置选项，此数据必须作为任务数据变量可用。", "examples_description": "如果您正在寻找一个起点，请尝试将这些示例字段添加到您的表单中，并根据您的需要进行更改。", "form_preview": "表单预览", "save_and_close": "保存并关闭", "instances": "实例", "more": "更多", "user_profile": "用户资料", "user": "用户", "process_status_running": "运行中", "process_status_complete": "已完成", "process_status_error": "错误", "process_status_suspended": "已暂停", "process_status_terminated": "已终止", "process_status_waiting": "等待中", "process_status_not_started": "未开始", "process_status_user_input_required": "需要用户输入", "task_state_ready": "就绪", "task_state_completed": "已完成", "task_state_waiting": "等待中", "task_state_error": "错误", "task_state_future": "未来", "task_state_likely": "可能", "task_state_maybe": "也许", "start_event": "开始时间", "end_event": "结束事件", "user_task": "用户任务", "service_task": "服务任务", "script_task": "脚本任务", "manual_task": "手动任务", "receive_task": "接收任务", "send_task": "发送任务", "business_rule_task": "业务规则任务", "exclusive_gateway": "排他网关", "parallel_gateway": "并行网关", "inclusive_gateway": "包容网关", "event_based_gateway": "事件网关", "subprocess": "子流程", "call_activity": "调用活动", "process_lastMilestone_started": "开始流程", "process_lastMilestone_completed": "流程已完成", "process_lastMilestone_error": "流程异常"}