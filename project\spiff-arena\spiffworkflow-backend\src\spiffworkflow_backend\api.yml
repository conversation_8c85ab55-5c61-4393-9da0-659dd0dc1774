openapi: "3.0.2"
info:
  version: 1.0.0
  title: spiffworkflow-backend
  license:
    name: LGPL
servers:
  - url: http://localhost:5000/v1.0
# this is handled in flask now
security: []

paths:
  /authentication-options:
    get:
      summary: redirect to open id authentication server
      operationId: spiffworkflow_backend.routes.authentication_controller.authentication_options
      tags:
        - Authentication
      responses:
        "200":
          description: Redirects to authentication server

  /login:
    parameters:
      - name: authentication_identifier
        in: query
        required: true
        schema:
          type: string
      - name: redirect_url
        in: query
        required: false
        schema:
          type: string
      - name: process_instance_id
        in: query
        required: false
        schema:
          type: integer
      - name: task_guid
        in: query
        required: false
        schema:
          type: string
    get:
      summary: redirect to open id authentication server
      operationId: spiffworkflow_backend.routes.authentication_controller.login
      tags:
        - Authentication
      responses:
        "200":
          description: Redirects to authentication server
  /login_return:
    parameters:
      - name: code
        in: query
        required: true
        schema:
          type: string
      - name: state
        in: query
        required: true
        schema:
          type: string
      - name: session_state
        in: query
        required: false
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.authentication_controller.login_return
      tags:
        - Authentication
      responses:
        "200":
          description: Test Return Response
  /logout:
    parameters:
      - name: id_token
        in: query
        required: true
        schema:
          type: string
      - name: redirect_url
        in: query
        required: false
        schema:
          type: string
      - name: authentication_identifier
        in: query
        required: true
        schema:
          type: string
      - name: backend_only
        in: query
        required: false
        schema:
          type: boolean
    get:
      operationId: spiffworkflow_backend.routes.authentication_controller.logout
      summary: Logout authenticated user
      tags:
        - Authentication
      responses:
        "200":
          description: Logout Authenticated User
  /logout_return:
    get:
      operationId: spiffworkflow_backend.routes.authentication_controller.logout_return
      summary: Logout authenticated user
      tags:
        - Authentication
      responses:
        "200":
          description: Logout Authenticated User

  /login_with_access_token:
    parameters:
      - name: access_token
        in: query
        required: true
        schema:
          type: string
      - name: authentication_identifier
        in: query
        required: true
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.authentication_controller.login_with_access_token
      summary: Authenticate user for API access with an openid token already posessed.
      tags:
        - Authentication
      responses:
        "200":
          description: "Returns ok: true if user successfully logged in."
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /login_api:
    parameters:
      - name: authentication_identifier
        in: query
        required: true
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.authentication_controller.login_api
      summary: Authenticate user for API access
      tags:
        - Authentication
      responses:
        "200":
          description: Redirects to authentication server
  /login_api_return:
    parameters:
      - name: code
        in: query
        required: true
        schema:
          type: string
      - name: state
        in: query
        required: true
        schema:
          type: string
      - name: session_state
        in: query
        required: false
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.authentication_controller.login_api_return
      tags:
        - Authentication
      responses:
        "200":
          description: Test Return Response

  /script-assist/enabled:
    get:
      operationId: spiffworkflow_backend.routes.script_assist_controller.enabled
      summary: Returns value of SCRIPT_ASSIST_ENABLED
      tags:
        - AI Tools
      responses:
        "200":
          description: Returns if AI script should be enabled in UI
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /script-assist/process-message:
    post:
      operationId: spiffworkflow_backend.routes.script_assist_controller.process_message
      summary: Send natural language message in for processing by AI service
      tags:
        - AI Tools
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                query:
                  type: string
                  description: The natural language message to be processed.
      responses:
        "200":
          description: Send back AI service response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /status:
    get:
      operationId: spiffworkflow_backend.routes.health_controller.status
      summary: Returns 200 if the server is Responding
      tags:
        - Liveness
        - Status
      responses:
        "200":
          description: The server is running.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /debug/test-raise-error:
    post:
      operationId: spiffworkflow_backend.routes.debug_controller.test_raise_error
      summary: Returns an unhandled exception that should notify sentry, if sentry is configured
      tags:
        - Status
      responses:
        "500":
          description: The server raises a test error as expected.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /debug/url-info:
    get:
      operationId: spiffworkflow_backend.routes.debug_controller.url_info
      summary: Returns information about the url of the application
      tags:
        - Status
      responses:
        "200":
          description: Returns url info
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /debug/version-info:
    get:
      operationId: spiffworkflow_backend.routes.debug_controller.version_info
      summary: Returns information about the version of the application
      tags:
        - Status
      responses:
        "200":
          description: Returns version info if it exists.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /debug/celery-backend-results/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        schema:
          type: integer
      - name: include_all_failures
        in: query
        required: false
        schema:
          type: boolean
    get:
      operationId: spiffworkflow_backend.routes.debug_controller.celery_backend_results
      summary: Returns the results from the celery backend for a process instance
      tags:
        - Status
      responses:
        "200":
          description: Returns results.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /onboarding:
    get:
      operationId: spiffworkflow_backend.routes.onboarding_controller.get_onboarding
      summary: Returns information about the next onboarding to show
      tags:
        - Status
      responses:
        "200":
          description: Returns info about the next onboarding to show if it exists.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /active-users/updates/{last_visited_identifier}:
    parameters:
      - name: last_visited_identifier
        in: path
        required: true
        description: The identifier for the last visited page for the user.
        schema:
          type: string
    post:
      tags:
        - Active User
      operationId: spiffworkflow_backend.routes.active_users_controller.active_user_updates
      summary: An SSE (Server Sent Events) endpoint that returns what users are also currently viewing the same page as you.
      responses:
        "200":
          description: list of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/User"

  /active-users/unregister/{last_visited_identifier}:
    parameters:
      - name: last_visited_identifier
        in: path
        required: true
        description: The identifier for the last visited page for the user.
        schema:
          type: string
    post:
      tags:
        - Active User
      operationId: spiffworkflow_backend.routes.active_users_controller.active_user_unregister
      summary: Unregisters a user from a page. To be used when the /active-users/updates endpoint is closed.
      responses:
        "200":
          description: The current user has unregistered.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-groups:
    parameters:
      - name: process_group_identifier
        in: query
        required: false
        description: Optional parameter to filter by a single group
        schema:
          type: string
      - name: filter_runnable_by_user
        in: query
        required: false
        description: Get only the process models that the user can run
        schema:
          type: boolean
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The number of groups to show per page. Defaults to 10.
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.process_groups_controller.process_group_list
      summary: get list
      tags:
        - Process Groups
      responses:
        "200":
          description: An array of process groups
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ProcessGroup"
    post:
      operationId: spiffworkflow_backend.routes.process_groups_controller.process_group_create
      summary: Add process group
      tags:
        - Process Groups
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProcessGroup"
      responses:
        "201":
          description: Processs Group
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessGroup"

  /process-groups/{modified_process_group_id}:
    parameters:
      - name: modified_process_group_id
        in: path
        required: true
        description: The unique id of an existing process group.
        schema:
          type: string
    # process_group_show
    get:
      operationId: spiffworkflow_backend.routes.process_groups_controller.process_group_show
      summary: Returns a single process group
      tags:
        - Process Groups
      responses:
        "200":
          description: Processs Group.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessGroup"
    delete:
      operationId: spiffworkflow_backend.routes.process_groups_controller.process_group_delete
      summary: Deletes a single process group
      tags:
        - Process Groups
      responses:
        "200":
          description: The process group was deleted.
    put:
      operationId: spiffworkflow_backend.routes.process_groups_controller.process_group_update
      summary: Updates a single process group
      tags:
        - Process Groups
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProcessGroup"
      responses:
        "200":
          description: Process group updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessGroup"

  /process-groups/{modified_process_group_identifier}/move:
    parameters:
      - name: modified_process_group_identifier
        in: path
        required: true
        description: The unique id of an existing process group.
        schema:
          type: string
      - name: new_location
        in: query
        required: true
        description: the new location, as an existing process group id
        schema:
          type: string
    put:
      operationId: spiffworkflow_backend.routes.process_groups_controller.process_group_move
      summary: returns the new group
      tags:
        - Process Groups
      responses:
        "200":
          description: Process Group
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessGroup"

  /upsearch-locations:
    parameters:
      - name: location
        in: query
        required: false
        description: The location to perform the upsearch
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.upsearch_controller.upsearch_locations
      summary: Returns upsearch locations for the given location
      tags:
        - Upsearch
      responses:
        "200":
          description: Upsearch Locations.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Locations"

  /process-models:
    parameters:
      - name: process_group_identifier
        in: query
        required: false
        description: The group containing the models we want to return
        schema:
          type: string
      - name: recursive
        in: query
        required: false
        description: Get all sub process models recursively if true
        schema:
          type: boolean
      - name: filter_runnable_by_user
        in: query
        required: false
        description: Get only the process models that the user can run
        schema:
          type: boolean
      - name: include_parent_groups
        in: query
        required: false
        description: Get the display names for the parent groups as well
        schema:
          type: boolean
      - name: group_by_process_group
        in: query
        required: false
        description: Returns a list of process_groups instead which include the process models. Useful alongside filter_runnable_by_user.
        schema:
          type: boolean
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The number of models to show per page. Defaults to 10.
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_list
      summary: Return a list of process models for a given process group
      tags:
        - Process Models
      responses:
        "200":
          description: Successfully return the requested models
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ProcessModel"

  /process-models/{modified_process_group_id}:
    parameters:
      - name: modified_process_group_id
        in: path
        required: true
        description: modified id of an existing process group
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_create
      summary: Creates a new process model with the given parameters.
      tags:
        - Process Models
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProcessModel"
      responses:
        "201":
          description: Process model created successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessModel"

  /process-model-natural-language/{modified_process_group_id}:
    parameters:
      - name: modified_process_group_id
        in: path
        required: true
        description: modified id of an existing process group
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_create_with_natural_language
      summary: Creates a new process model with the given parameters.
      tags:
        - Process Models
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProcessModel"
      responses:
        "201":
          description: Process model created successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessModel"

  /process-model-tests/run/{modified_process_model_identifier}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The process_model_id, modified to replace slashes (/)
        schema:
          type: string
      - name: test_case_file
        in: query
        required: false
        description: The name of the test case file to run
        schema:
          type: string
      - name: test_case_identifier
        in: query
        required: false
        description: The name of the test case file to run
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_test_run
      summary: Run a test for a process model
      tags:
        - Process Model Tests
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "201":
          description: Metadata about the uploaded file, but not the file content.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/File"

  /process-model-tests/create/{modified_process_model_identifier}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The process_model_id, modified to replace slashes (/)
        schema:
          type: string
      - name: test_case_identifier
        in: query
        required: false
        description: The name of the test case file to run
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_test_generate
      summary: Create a bpmn unit test from a process instance
      tags:
        - Process Model Tests
      requestBody:
        content:
          application/json:
            schema:
              properties:
                process_instance_id:
                  description: The id of the associated process instance
                  type: number
                  example: 2
                  nullable: false
                test_case_identifier:
                  description: The name for the new test case
                  type: string
                  nullable: true
      responses:
        "201":
          description: The generated bpmn unit test
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AwesomeUnspecifiedPayload"

  /process-models/{modified_process_model_identifier}/files:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The process_model_id, modified to replace slashes (/)
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_file_create
      summary: Add a new workflow spec file
      tags:
        - Process Model Files
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "201":
          description: Metadata about the uploaded file, but not the file content.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/File"

  /process-models/{modified_process_model_identifier}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: the modified process model id
        schema:
          type: string
      - name: include_file_references
        in: query
        required: false
        description: include all file references in the return
        schema:
          type: boolean
    get:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_show
      summary: Returns a single process model
      tags:
        - Process Models
      responses:
        "200":
          description: Workflow spec.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessModel"
    put:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_update
      summary: Modifies an existing process model with the given parameters.
      tags:
        - Process Models
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProcessModel"
      responses:
        "200":
          description: Process model updated successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessModel"
    delete:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_delete
      summary: Removes an existing process model
      tags:
        - Process Models
      responses:
        "200":
          description: The process model has been removed.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-models/{modified_process_model_identifier}/move:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: the modified process model id
        schema:
          type: string
      - name: new_location
        in: query
        required: true
        description: the new location for the process model, as a process group id
        schema:
          type: string
    put:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_move
      summary: returns the new model
      tags:
        - Process Models
      responses:
        "200":
          description: Process Model
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessModel"

  /files/{file_id}/download:
    parameters:
      - name: file_id
        in: path
        required: true
        description: The ID of the file to download
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.file_download_controller.unified_download
      summary: Download a file by its ID
      tags:
        - Files
      responses:
        "200":
          description: The file content
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
      
  /process-model-publish/{modified_process_model_identifier}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: the modified process model id
        schema:
          type: string
      - name: branch_to_update
        in: query
        required: false
        description: the name of the branch we want to merge into
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_publish
      summary: Merge changes from this model to another branch.
      tags:
        - Process Models
      responses:
        "200":
          description: The process model was published.
          content:
            application/json:
              schema:
                type: string

  /processes/callers/{bpmn_process_identifiers}:
    parameters:
      - name: bpmn_process_identifiers
        in: path
        required: true
        description: the bpmn process identifiers/ids (not the names with spaces and not the process model identifiers)
        schema:
          type: array
          items:
            type: string
          minItems: 1
    get:
      operationId: spiffworkflow_backend.routes.process_api_blueprint.process_caller_list
      summary: Return a list of information about all processes that call the provided process id
      tags:
        - Process Models
      responses:
        "200":
          description: Successfully return the requested calling processes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Process"

  /processes:
    get:
      operationId: spiffworkflow_backend.routes.process_api_blueprint.process_list
      summary:
        Return a list of all processes (not just primary process of a process model)
        useful for finding processes for call activites.
      tags:
        - Process Models
      responses:
        "200":
          description: Successfully return the requested processes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Process"

  /github-webhook-receive:
    post:
      operationId: spiffworkflow_backend.routes.webhooks_controller.github_webhook_receive
      summary: receives push webhooks from github so we can keep our process model repo up to date
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      tags:
        - git
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /webhook:
    post:
      operationId: spiffworkflow_backend.routes.webhooks_controller.webhook
      summary: receives webhooks from external systems and runs a process model using the data received from the caller so arbitrary handling can be achieved.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      tags:
        - git
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instances/for-me:
    parameters:
      - name: process_model_identifier
        in: query
        required: false
        description: The unique id of an existing process model.
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_list_for_me
      summary: Returns a list of process instances that are associated with me.
      tags:
        - Process Instances
      requestBody:
        description: Report dictionary to use.
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProcessInstanceReport"
      responses:
        "200":
          description: Workflow.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Workflow"

  /process-instances:
    parameters:
      - name: process_model_identifier
        in: query
        required: false
        description: The unique id of an existing process model.
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_list
      summary: Returns a list of process instances.
      tags:
        - Process Instances
      requestBody:
        description: Report dictionary to use.
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProcessInstanceReport"
      responses:
        "200":
          description: Workflow.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Workflow"

  /extensions:
    get:
      operationId: spiffworkflow_backend.routes.extensions_controller.extension_list
      summary: Returns the list of available extensions
      tags:
        - Extensions
      responses:
        "200":
          description: Resulting extensions
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /extensions/{modified_process_model_identifier}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model.
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.extensions_controller.extension_show
      summary: Returns the metadata for a given extension
      tags:
        - Extensions
      responses:
        "200":
          description: Resulting extension metadata
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"
    post:
      operationId: spiffworkflow_backend.routes.extensions_controller.extension_run
      summary: Run an extension for a given process model
      tags:
        - Extensions
      responses:
        "200":
          description: Resulting task data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /extensions-get-data/{query_params}:
    parameters:
      - name: query_params
        in: path
        required: true
        description: The params required to run the extension. The first parameter must be the modified_process_model_identifier of the extension to run.
        schema:
          type: string
          format: path
    get:
      operationId: spiffworkflow_backend.routes.extensions_controller.extension_get_data
      summary: Returns the metadata for a given extension
      tags:
        - Extensions
      responses:
        "200":
          description: Resulting extension metadata
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /process-models/{modified_process_model_identifier}/script-unit-tests:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model.
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.script_unit_tests_controller.script_unit_test_create
      summary: Create script unit test based on given criteria
      tags:
        - Script Unit Test
      responses:
        "200":
          description: Script Unit Test Result
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /process-models/{modified_process_model_identifier}/script-unit-tests/run:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model.
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.script_unit_tests_controller.script_unit_test_run
      summary: Run a given script unit test.
      tags:
        - Script Unit Test
      responses:
        "200":
          description: Script Unit Test Result
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /process-instances/{modified_process_model_identifier}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model.
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_create
      summary: Creates an process instance from a process model and returns the instance
      tags:
        - Process Instances
      responses:
        "201":
          description: Workflow generated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /process-instances/for-me/{modified_process_model_identifier}/{process_instance_id}/task-info:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: process_identifier
        in: query
        required: false
        description: The identifier of the process to use for the diagram. Useful for displaying the diagram for a call activity.
        schema:
          type: string
      - name: most_recent_tasks_only
        in: query
        required: false
        description: If true, this wil return only the most recent tasks.
        schema:
          type: boolean
      - name: bpmn_process_guid
        in: query
        required: false
        description: The guid of the bpmn process to get the tasks for.
        schema:
          type: string
      - name: to_task_guid
        in: query
        required: false
        description: Get the tasks only up to the given guid.
        schema:
          type: string
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_task_list_without_task_data_for_me
      summary: returns the list of all user tasks associated with process instance without the task data
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /process-instances/{modified_process_model_identifier}/{process_instance_id}/task-info:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: process_identifier
        in: query
        required: false
        description: The identifier of the process to use for the diagram. Useful for displaying the diagram for a call activity.
        schema:
          type: string
      - name: most_recent_tasks_only
        in: query
        required: false
        description: If true, this wil return only the most recent tasks.
        schema:
          type: boolean
      - name: bpmn_process_guid
        in: query
        required: false
        description: The guid of the bpmn process to get the tasks for.
        schema:
          type: string
      - name: to_task_guid
        in: query
        required: false
        description: Get the tasks only up to the given guid.
        schema:
          type: string
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_task_list_without_task_data
      summary: returns the list of all user tasks associated with process instance without the task data
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /process-instances/for-me/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: process_identifier
        in: query
        required: false
        description: The identifier of the process to use for the diagram. Useful for displaying the diagram for a call activity.
        schema:
          type: string
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_show_for_me
      summary: Show information about a process instance that is associated with me
      responses:
        "200":
          description: One Process Instance
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /process-instances/find-by-id/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_find_by_id
      summary: Find a process instance based on its id only
      tags:
        - Process Instances
      responses:
        "200":
          description: One Process Instance
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /process-instances/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: process_identifier
        in: query
        required: false
        description: The identifier of the process to use for the diagram. Useful for displaying the diagram for a call activity.
        schema:
          type: string
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_show
      summary: Show information about a process instance
      responses:
        "200":
          description: One Process Instance
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"
    delete:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_delete
      summary: Deletes a single process instance
      tags:
        - Process Instances
      responses:
        "200":
          description: The process instance was deleted.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instances/{modified_process_model_identifier}/{process_instance_id}/check-can-migrate:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The unique id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: target_bpmn_process_hash
        in: query
        required: false
        description: The full bpmn process hash of the target version of the process model.
        schema:
          type: string
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_check_can_migrate
      summary: Checks if a given process instance can be migrated to the newest version of the process model.
      responses:
        "200":
          description: The result
          content:
            application/json:
              schema:
                properties:
                  can_migrate:
                    type: boolean
                    description: True if it can migrate and false if not.
                  process_instance_id:
                    type: integer
                    description: Process instance id.

  /process-instances/{modified_process_model_identifier}/{process_instance_id}/run:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: force_run
        in: query
        required: false
        description: Force the process instance to run even if it has already been started.
        schema:
          type: boolean
      - name: execution_mode
        in: query
        required: false
        description: Either run in "synchronous" or "asynchronous" mode.
        schema:
          type: string
          enum:
            - synchronous
            - asynchronous
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_run
      summary: Run a process instance
      tags:
        - Process Instances
      responses:
        "200":
          description: Returns details about the workflows state and current task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /process-instance-terminate/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_terminate
      summary: Terminate a process instance
      tags:
        - Process Instances
      responses:
        "200":
          description: Empty ok true response on successful termination.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instance-suspend/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_suspend
      summary: Suspend a process instance
      tags:
        - Process Instances
      responses:
        "200":
          description: Empty ok true response on successful suspension.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instance-resume/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_resume
      summary: Resume a process instance
      tags:
        - Process Instances
      responses:
        "200":
          description: Empty ok true response on successful resume.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instance-reset/{modified_process_model_identifier}/{process_instance_id}/{to_task_guid}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified process model id
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: to_task_guid
        in: path
        required: true
        description: Get the tasks only up to the given guid.
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_reset
      summary: Reset a process instance to an earlier step
      tags:
        - Process Instances
      responses:
        "200":
          description: Empty ok true response on successful reset.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instance-migrate/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified process model id
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: target_bpmn_process_hash
        in: query
        required: false
        description: The full bpmn process hash of the target version of the process model.
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_migrate
      summary: Migrate a process instance to the new version of its process model.
      tags:
        - Process Instances
      responses:
        "200":
          description: Empty ok true response on successful reset.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instances/reports:
    parameters:
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_report_list
      summary: Returns all process instance reports for process model
      tags:
        - Process Instances
        - Process Instances Reports
      responses:
        "200":
          description: Process Instance Report
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ProcessInstanceReport"
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_report_create
      summary: Returns all process instance reports for process model
      tags:
        - Process Instances
      responses:
        "201":
          description: The process instance report was created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-instances/reports/columns:
    parameters:
      - name: process_model_identifier
        in: query
        required: false
        description: The process model identifier to filter by
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_report_column_list
      summary: Returns all available columns for a process instance report, including custom metadata
      tags:
        - Process Instances
      responses:
        "200":
          description: Workflow.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Workflow"

  /process-instances/report-metadata:
    parameters:
      - name: report_hash
        in: query
        required: false
        description: The hash of a query that has been searched before.
        schema:
          type: string
      - name: report_id
        in: query
        required: false
        description: The unique id of an existing report.
        schema:
          type: integer
      - name: report_identifier
        in: query
        required: false
        description: Specifies the identifier of a report to use, if any
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_report_show
      summary: Returns the metadata associated with a given report key. This favors report_hash over report_id and report_identifier.
      tags:
        - Process Instances
      responses:
        "200":
          description: Workflow.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Workflow"

  /process-instances/reports/{report_id}:
    parameters:
      - name: report_id
        in: path
        required: true
        description: The unique id of an existing report
        schema:
          type: integer
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    put:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_report_update
      summary: Updates a process instance report
      tags:
        - Process Instances
      responses:
        "200":
          description: The process instance report was updated.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"
    delete:
      operationId: spiffworkflow_backend.routes.process_instances_controller.process_instance_report_delete
      summary: Delete a process instance report
      tags:
        - Process Instances
      responses:
        "200":
          description: The process instance report was delete.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-models/{modified_process_model_identifier}/specs:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified process model id
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_specs
      summary: Returns process model specs
      tags:
        - Process Model Specs
      responses:
        "200":
          description: Returns the spec requested.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-models/{modified_process_model_identifier}/files/{file_name}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified process model id
        schema:
          type: string
      - name: file_name
        in: path
        required: true
        description: The id of the spec file
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_file_show
      summary: Returns metadata about the file
      tags:
        - Process Model Files
      responses:
        "200":
          description: Returns the file information requested.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/File"
    put:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_file_update
      summary: save the contents to the given file
      parameters:
        - name: file_contents_hash
          in: query
          required: false
          description: The hash of the file contents that originally came with the file.
          schema:
            type: string
      tags:
        - Process Model Files
      requestBody:
        description: Log Pagination Request
        required: false
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        "200":
          description: Metadata about the uploaded file, but not the file content.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"
    delete:
      operationId: spiffworkflow_backend.routes.process_models_controller.process_model_file_delete
      summary: Removes an existing process model file
      tags:
        - Process Model Files
      responses:
        "200":
          description: The process model has been removed.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /tasks:
    parameters:
      - name: process_instance_id
        in: query
        required: false
        description: The process instance id to search by.
        schema:
          type: integer
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    get:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.task_list_my_tasks
      summary: returns the list of ready or waiting tasks for a user
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /tasks/for-my-open-processes:
    parameters:
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.tasks_controller.task_list_for_my_open_processes
      summary: returns the list of tasks for given user's open process instances
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /tasks/for-me:
    parameters:
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.tasks_controller.task_list_for_me
      summary: returns the list of tasks for given user's open process instances
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /tasks/for-my-groups:
    parameters:
      - name: user_group_identifier
        in: query
        required: false
        description: The identifier of the group to get the tasks for
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.tasks_controller.task_list_for_my_groups
      summary: returns the list of tasks for given user's open process instances
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /tasks/completed-by-me/{process_instance_id}:
    parameters:
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.tasks_controller.task_list_completed_by_me
      summary: returns the list of tasks for that the current user has completed
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /tasks/completed/{process_instance_id}:
    parameters:
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.tasks_controller.task_list_completed
      summary: returns the list of human tasks that have been completed
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /tasks/progress/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    get:
      tags:
        - Process Instances
      operationId: spiffworkflow_backend.routes.tasks_controller.process_instance_progress
      summary: returns the list of instructions that have been queued for a process instance.
      responses:
        "200":
          description: list of task instructions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/TaskInstructionsForEndUser"

  /users/search:
    parameters:
      - name: username_prefix
        in: query
        required: true
        description: The prefix of the user
        schema:
          type: string
    get:
      tags:
        - Users
      operationId: spiffworkflow_backend.routes.users_controller.user_search
      summary: Returns a list of users that the search param
      responses:
        "200":
          description: list of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/User"

  /users/exists/by-username:
    post:
      tags:
        - Users
      operationId: spiffworkflow_backend.routes.users_controller.user_exists_by_username
      summary: Returns a true if user exists by username.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "200":
          description: true if user exists
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /user-groups/for-current-user:
    get:
      tags:
        - User Groups
      operationId: spiffworkflow_backend.routes.users_controller.user_group_list_for_current_user
      summary: Group identifiers for current logged in user
      responses:
        "200":
          description: list of user groups
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /task-data/{modified_process_model_identifier}/{process_instance_id}/{task_guid}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: task_guid
        in: path
        required: true
        description: The unique id of the task.
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.tasks_controller.task_data_show
      summary: Get task data for a single task.
      tags:
        - Process Instances
      responses:
        "200":
          description: list of tasks
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"
    put:
      operationId: spiffworkflow_backend.routes.tasks_controller.task_data_update
      summary: Update the task data for requested instance and task
      tags:
        - Process Instances
      responses:
        "200":
          description: Task Updated Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /task-assign/{modified_process_model_identifier}/{process_instance_id}/{task_guid}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: task_guid
        in: path
        required: true
        description: The unique id of the task.
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.tasks_controller.task_assign
      summary: Assign a given task to a list of additional users
      tags:
        - Process Instances
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/User"
      responses:
        "200":
          description: "ok: true"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /process-data/{category}/{modified_process_model_identifier}/{process_data_identifier}/{process_instance_id}:
    parameters:
      - name: category
        in: path
        required: true
        description: The category of the data object.
        schema:
          type: string
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: process_data_identifier
        in: path
        required: true
        description: The identifier of the process data.
        schema:
          type: string
      - name: process_identifier
        in: query
        required: false
        description: "DEPRECATED - only the bpmn_process_guid is needed now: The identifier of the process the data object is in."
        schema:
          type: string
      - name: bpmn_process_guid
        in: query
        required: false
        description: The guid of the bpmn process to get the data object for.
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.process_api_blueprint.process_data_show
      summary: Fetch the process data value.
      tags:
        - Data Objects
      responses:
        "200":
          description: Fetch succeeded.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /send-event/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of the process instance
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.send_bpmn_event
      summary: Send a BPMN event to the process
      tags:
        - Process Instances
      responses:
        "200":
          description: Event Sent Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /task-complete/{modified_process_model_identifier}/{process_instance_id}/{task_guid}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of the process instance
        schema:
          type: integer
      - name: task_guid
        in: path
        required: true
        description: The unique id of the task.
        schema:
          type: string
    post:
      operationId: spiffworkflow_backend.routes.tasks_controller.manual_complete_task
      summary: Mark a task complete without executing it
      tags:
        - Process Instances
      responses:
        "200":
          description: Event Sent Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /service-tasks:
    get:
      tags:
        - Service Tasks
      operationId: spiffworkflow_backend.routes.service_tasks_controller.service_task_list
      summary: Gets all available service task connectors
      responses:
        "200":
          description: All service task connectors
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceTask"

  /authentications:
    get:
      tags:
        - Authentications
      operationId: spiffworkflow_backend.routes.service_tasks_controller.authentication_list
      summary: Gets all available authentications from connector proxy
      responses:
        "200":
          description: All authentications
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceTask"

  /authentication/configuration:
    get:
      tags:
        - Authentications
      operationId: spiffworkflow_backend.routes.service_tasks_controller.authentication_configuration
      summary: Gets authentication configurations
      responses:
        "200":
          description: Authentication configuration
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceTask"
    put:
      operationId: spiffworkflow_backend.routes.service_tasks_controller.authentication_configuration_update
      summary: Save the authentication configuration
      tags:
        - Authentication
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ServiceTask"
      responses:
        "200":
          description: Authentication configuration updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /authentication_begin/{service}/{auth_method}:
    parameters:
      - name: service
        in: path
        required: true
        description: The name of the service
        schema:
          type: string
      - name: auth_method
        in: path
        required: true
        description: The method
        schema:
          type: string
    get:
      tags:
        - Authentications
      operationId: spiffworkflow_backend.routes.service_tasks_controller.authentication_begin
      summary: Begin an external authentication
      responses:
        "200":
          description: All authentications
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceTask"

  /authentication_callback/{service}/{auth_method}:
    parameters:
      - name: service
        in: path
        required: true
        description: The name of the service
        schema:
          type: string
      - name: auth_method
        in: path
        required: true
        description: The method
        schema:
          type: string
      - name: response
        in: query
        required: false
        description: The response
        schema:
          type: string
      - name: token
        in: query
        required: false
        description: The response
        schema:
          type: string
      - name: code
        in: query
        required: false
        description: The response
        schema:
          type: string
      - name: code_challenge
        in: query
        required: false
        description: The response
        schema:
          type: string
      - name: code_challenge_method
        in: query
        required: false
        description: The response
        schema:
          type: string
      - name: state
        in: query
        required: false
        description: The response
        schema:
          type: string
    get:
      tags:
        - Authentications
      operationId: spiffworkflow_backend.routes.service_tasks_controller.authentication_callback
      summary: Callback to backend
      responses:
        "200":
          description: All authentications
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceTask"

  /tasks/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: execute_tasks
        in: query
        required: false
        description: Execute ready tasks on the process instance.
        schema:
          type: boolean
    get:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.interstitial
      summary: An SSE (Server Sent Events) endpoint that returns what tasks are currently active (running, waiting, or the final END event)
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"

  /tasks/prepare-form:
    post:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.prepare_form
      summary: Pre-processes a form schema, ui schema, and data, useful for previewing a form
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Task"
      responses:
        "200":
          description: Returns the same data structure provided, but after some replacements.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"

  /tasks/{process_instance_id}/instruction:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    get:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.task_with_instruction
      summary: Gets the next task and its instructions
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"

  /tasks/{process_instance_id}/{task_guid}/task-instances:
    parameters:
      - name: task_guid
        in: path
        required: true
        description: The unique id of an existing process group.
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    get:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.task_instance_list
      summary: Gets all tasks that have the same bpmn identifier and are in the same bpmn process as the given task guid.
      responses:
        "200":
          description: All relevant tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Task"

  /tasks/{process_instance_id}/{task_guid}:
    parameters:
      - name: task_guid
        in: path
        required: true
        description: The unique id of an existing process group.
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: with_form_data
        in: query
        required: false
        description: Include task data for forms
        schema:
          type: boolean
      - name: execution_mode
        in: query
        required: false
        description: Either run in "synchronous" or "asynchronous" mode.
        schema:
          type: string
          enum:
            - synchronous
            - asynchronous
    get:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.task_show
      summary: Gets one task that a user wants to complete
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"
    put:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.task_submit
      summary: Update the form data for a tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"
        "202":
          description: "ok: true"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /tasks/{process_instance_id}/{task_guid}/save-draft:
    parameters:
      - name: task_guid
        in: path
        required: true
        description: The unique id of an existing process group.
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    post:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.task_save_draft
      summary: Update the draft form for this task
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"
        "202":
          description: "ok: true"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /tasks/{process_instance_id}/{task_guid}/allows-guest:
    parameters:
      - name: task_guid
        in: path
        required: true
        description: The unique id of an existing process group.
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
    get:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.tasks_controller.task_allows_guest
      summary: Gets checks if the given task allows guest login
      responses:
        "200":
          description: Whether the task can be completed by a guest
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TaskAllowsGuest"

  # NOTE: this should probably be /process-instances instead
  /tasks/{process_instance_id}/send-user-signal-event:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of the process instance
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.process_instances_controller.send_user_signal_event
      summary: Send a BPMN event to the process
      tags:
        - Process Instances
      responses:
        "200":
          description: Event Sent Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /message-models:
    get:
      tags:
        - Messages
      operationId: spiffworkflow_backend.routes.messages_controller.message_model_list_from_root
      summary: Get a list of message models
      responses:
        "200":
          description: A list of message models
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /message-models/{relative_location}:
    parameters:
      - name: relative_location
        in: path
        required: true
        description: The location of the message model relative to the root of the project, defaults to /
        schema:
          type: string
    get:
      tags:
        - Messages
      operationId: spiffworkflow_backend.routes.messages_controller.message_model_list
      summary: Get a list of message models
      responses:
        "200":
          description: A list of message models
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /messages:
    parameters:
      - name: process_instance_id
        in: query
        required: false
        description: the id of the process instance
        schema:
          type: integer
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The number of models to show per page. Defaults to 10.
        schema:
          type: integer
      - name: message_category
        in: query
        required: false
        description: Filter by message category (1=internal, 2=sms, 3=email)
        schema:
          type: string
      - name: search
        in: query
        required: false
        description: Search in recipient, message name, or content
        schema:
          type: string
      - name: status
        in: query
        required: false
        description: Filter by message status (ready, running, completed, failed)
        schema:
          type: string
      - name: start_date
        in: query
        required: false
        description: Filter messages created after this date (YYYY-MM-DD)
        schema:
          type: string
      - name: end_date
        in: query
        required: false
        description: Filter messages created before this date (YYYY-MM-DD)
        schema:
          type: string
    get:
      tags:
        - Messages
      operationId: spiffworkflow_backend.routes.messages_controller.message_instance_list
      summary: Get a list of message instances
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"

  /messages/{modified_message_name}:
    parameters:
      - name: modified_message_name
        in: path
        required: true
        description: The message_name, modified to replace slashes (/) with colons
        schema:
          type: string
      - name: execution_mode
        in: query
        required: false
        description: Either run in "synchronous" or "asynchronous" mode.
        schema:
          type: string
          enum:
            - synchronous
            - asynchronous
    post:
      tags:
        - Messages
      operationId: spiffworkflow_backend.routes.messages_controller.message_send
      summary: Instantiate and run a given process model with a message start event matching given name
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                properties:
                  task_data:
                    $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
                  process_instance:
                    $ref: "#/components/schemas/AwesomeUnspecifiedPayload"

  /public/messages/form/{modified_message_name}:
    parameters:
      - name: modified_message_name
        in: path
        required: true
        description: The message_name, modified to replace slashes (/) with colons
        schema:
          type: string
    get:
      tags:
        - Messages
      operationId: spiffworkflow_backend.routes.public_controller.message_form_show
      summary: Gets the form associated with the given message name.
      responses:
        "200":
          description: The json schema form.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AwesomeUnspecifiedPayload"

  /public/messages/submit/{modified_message_name}:
    parameters:
      - name: modified_message_name
        in: path
        required: true
        description: The message_name, modified to replace slashes (/) with colons
        schema:
          type: string
      - name: execution_mode
        in: query
        required: false
        description: Either run in "synchronous" or "asynchronous" mode.
        schema:
          type: string
          enum:
            - synchronous
            - asynchronous
    post:
      tags:
        - Messages
      operationId: spiffworkflow_backend.routes.public_controller.message_form_submit
      summary: Instantiate and run a given process model with a message start event matching given name
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                properties:
                  task_data:
                    $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
                  process_instance:
                    $ref: "#/components/schemas/AwesomeUnspecifiedPayload"

  /public/tasks/{process_instance_id}/{task_guid}:
    parameters:
      - name: task_guid
        in: path
        required: true
        description: The unique id of an existing process group.
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: execution_mode
        in: query
        required: false
        description: Either run in "synchronous" or "asynchronous" mode.
        schema:
          type: string
          enum:
            - synchronous
            - asynchronous
    get:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.public_controller.form_show
      summary: Gets one task that a user wants to complete
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"
    put:
      tags:
        - Tasks
      operationId: spiffworkflow_backend.routes.public_controller.form_submit
      summary: Update the form data for a tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "200":
          description: One task
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"
        "202":
          description: "ok: true"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OkTrue"

  /logs/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: the id of the process instance
        schema:
          type: integer
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The number of items to show per page. Defaults to 10.
        schema:
          type: integer
      - name: events
        in: query
        required: false
        description: Show the events view, which includes all log entries
        schema:
          type: boolean
      - name: bpmn_name
        in: query
        required: false
        description: The bpmn name of the task to search for.
        schema:
          type: string
      - name: bpmn_identifier
        in: query
        required: false
        description: The bpmn identifier of the task to search for.
        schema:
          type: string
      - name: task_type
        in: query
        required: false
        description: The task type of the task to search for.
        schema:
          type: string
      - name: event_type
        in: query
        required: false
        description: The type of the event to search for.
        schema:
          type: string
    get:
      tags:
        - Process Instance Events
      operationId: spiffworkflow_backend.routes.process_instance_events_controller.log_list
      summary: returns a list of logs associated with the process instance
      responses:
        "200":
          description: list of logs
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessInstanceLog"

  /process-instance-events/{modified_process_model_identifier}/{process_instance_id}/migration:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: the id of the process instance
        schema:
          type: integer
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The process_model_id, modified to replace slashes (/)
        schema:
          type: string
    get:
      tags:
        - Process Instance Events
      operationId: spiffworkflow_backend.routes.process_instance_events_controller.process_instance_migration_event_list
      summary: returns a list of migration events associated with the process instance
      responses:
        "200":
          description: list of logs
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessInstanceLog"

  /logs/typeahead-filter-values/{modified_process_model_identifier}/{process_instance_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: the id of the process instance
        schema:
          type: integer
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The process_model_id, modified to replace slashes (/)
        schema:
          type: string
      - name: task_type
        in: query
        required: false
        description: The task type of the typehead filter values to get.
        schema:
          type: string
    get:
      tags:
        - Process Instance Events
      operationId: spiffworkflow_backend.routes.process_instance_events_controller.typeahead_filter_values
      summary: returns a list of task types and event typs. useful for building log queries.
      responses:
        "200":
          description: list of types
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessInstanceLog"

  /event-error-details/{modified_process_model_identifier}/{process_instance_id}/{process_instance_event_id}:
    parameters:
      - name: process_instance_id
        in: path
        required: true
        description: the id of the process instance
        schema:
          type: integer
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The process_model_id, modified to replace slashes (/)
        schema:
          type: string
      - name: process_instance_event_id
        in: path
        required: true
        description: the id of the process instance event
        schema:
          type: integer
    get:
      tags:
        - Process Instance Events
      operationId: spiffworkflow_backend.routes.process_instance_events_controller.error_detail_show
      summary: returns the error details for a given process instance event.
      responses:
        "200":
          description: list of types
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProcessInstanceLog"

  /secrets:
    parameters:
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The number of items to show per page. Defaults to 10.
        schema:
          type: integer
    post:
      operationId: spiffworkflow_backend.routes.secrets_controller.secret_create
      summary: Create a secret for a key and value
      tags:
        - Secrets
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Secret"
      responses:
        "201":
          description: Secret created successfully
          content:
            application/json:
              schema:
                type: number
    get:
      operationId: spiffworkflow_backend.routes.secrets_controller.secret_list
      summary: Return list of all secrets
      tags:
        - Secrets
      responses:
        "200":
          description: list of secrets
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Secret"

  /secrets/{key}:
    parameters:
      - name: key
        in: path
        required: true
        description: The key we are using
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.secrets_controller.secret_show
      summary: Return info about a secret for a key. Does not include the value.
      tags:
        - Secrets
      responses:
        "200":
          description: We return a secret
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Secret"
    delete:
      operationId: spiffworkflow_backend.routes.secrets_controller.secret_delete
      summary: Delete an existing secret
      tags:
        - Secrets
      responses:
        "204":
          description: The secret is deleted
        "401":
          description: Unauthorized to delete secret
        "404":
          description: Secret does not exist
    put:
      operationId: spiffworkflow_backend.routes.secrets_controller.secret_update
      summary: Modify an existing secret
      tags:
        - Secrets
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Secret"
      responses:
        "200":
          description: Secret updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Secret"
        "401":
          description: Unauthorized to update secret
        "404":
          description: Secret does not exist

  /secrets/show-value/{key}:
    parameters:
      - name: key
        in: path
        required: true
        description: The key we are using
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.secrets_controller.secret_show_value
      summary: Return a secret value for a key
      tags:
        - Secrets
      responses:
        "200":
          description: We return a secret
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Secret"

  /permissions-check:
    post:
      operationId: spiffworkflow_backend.routes.process_api_blueprint.permissions_check
      summary: Checks if current user has access to given list of target uris and permissions.
      tags:
        - Permissions
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AwesomeUnspecifiedPayload"
      responses:
        "200":
          description: Result of permission check
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Secret"

  /connector-proxy/typeahead/{category}:
    parameters:
      - name: category
        in: path
        required: true
        description: The category for the typeahead search
        schema:
          type: string
      - name: prefix
        in: query
        required: true
        description: The prefix to search for
        schema:
          type: string
      - name: limit
        in: query
        required: true
        description: The maximum number of search results
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.connector_proxy_controller.typeahead
      summary: Return type ahead search results
      tags:
        - Type Ahead
      responses:
        "200":
          description: We return type ahead search results
          #content:
          #  - application/json

  /data-stores:
    parameters:
      - name: process_group_identifier
        in: query
        required: false
        description: Optional parameter to filter by a single group
        schema:
          type: string
      - name: upsearch
        in: query
        required: false
        description: Optional parameter to indicate if an upsearch should be performed
        schema:
          type: boolean
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The number of groups to show per page. Defaults to 10.
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.data_store_controller.data_store_list
      summary: Return a list of the data store objects.
      tags:
        - Data Stores
      responses:
        "200":
          description: The list of currently defined data store objects
    post:
      operationId: spiffworkflow_backend.routes.data_store_controller.data_store_create
      summary: Create a new data store instance.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DataStore"
      tags:
        - Data Stores
      responses:
        "200":
          description: The newly created data store instance
    put:
      operationId: spiffworkflow_backend.routes.data_store_controller.data_store_update
      summary: Update a data store instance.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DataStore"
      tags:
        - Data Stores
      responses:
        "200":
          description: The updated data store instance
  /data-stores/types:
    get:
      operationId: spiffworkflow_backend.routes.data_store_controller.data_store_types
      summary: Return a list of the data store types.
      tags:
        - Data Stores
      responses:
        "200":
          description: The list of currently defined data store types
  /data-stores/{data_store_type}/{identifier}/items:
    parameters:
      - name: data_store_type
        in: path
        required: true
        description: The type of datastore, such as "typeahead"
        schema:
          type: string
      - name: identifier
        in: path
        required: true
        description: The identifier of the datastore, such as "cities"
        schema:
          type: string
      - name: location
        in: query
        required: false
        description: The location of the data store
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: The page number to return. Defaults to page 1.
        schema:
          type: integer
    get:
      operationId: spiffworkflow_backend.routes.data_store_controller.data_store_item_list
      summary: Returns a paginated list of the contents of a data store.
      tags:
        - Data Stores
      responses:
        "200":
          description: A list of the data stored in the requested data store.
  /data-stores/{data_store_type}/{identifier}:
    parameters:
      - name: data_store_type
        in: path
        required: true
        description: The type of datastore, such as "typeahead"
        schema:
          type: string
      - name: identifier
        in: path
        required: true
        description: The identifier of the datastore, such as "cities"
        schema:
          type: string
      - name: process_group_identifier
        in: query
        required: true
        description: The process group
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.data_store_controller.data_store_show
      summary: Returns a description of the data store.
      tags:
        - Data Stores
      responses:
        "200":
          description: The requested data store.

  /process-data-file-download/{modified_process_model_identifier}/{process_instance_id}/{process_data_identifier}:
    parameters:
      - name: modified_process_model_identifier
        in: path
        required: true
        description: The modified id of an existing process model
        schema:
          type: string
      - name: process_instance_id
        in: path
        required: true
        description: The unique id of an existing process instance.
        schema:
          type: integer
      - name: process_data_identifier
        in: path
        required: true
        description: The identifier of the process data.
        schema:
          type: string
    get:
      operationId: spiffworkflow_backend.routes.process_api_blueprint.process_data_file_download
      summary: Download the file referneced in the process data value.
      tags:
        - Data Objects
      responses:
        "200":
          description: Fetch succeeded.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workflow"
                
components:
  securitySchemes:
    oAuth2AuthCode:
      type: oauth2
      description: authenticate with openid server
      flows:
        authorizationCode:
          authorizationUrl: /v1.0/login_api
          tokenUrl: /v1.0/login_api_return
          scopes:
            read_email: read email
      x-tokenInfoFunc: spiffworkflow_backend.routes.authentication_controller.get_scope

  schemas:
    OkTrue:
      properties:
        ok:
          type: boolean
          example: true
    Locations:
      properties:
        locations:
          type: array
    User:
      properties:
        uid:
          type: string
        email_address:
          type: string
        display_name:
          type: string
        affiliation:
          type: string
        eppn:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        title:
          type: string
    DataModel:
      properties:
        id:
          type: string
    ProcessModelDiffList:
      properties:
        workflow_spec_id:
          type: string
          example: top_level_workflow
        created_at_in_seconds:
          type: integer
        location:
          type: string
          example: remote
        new:
          type: boolean
          example: false
    ProcessModelFilesList:
      properties:
        file_model_id:
          type: integer
          example: 171
        workflow_spec_id:
          type: string
          example: top_level_workflow
        filename:
          type: string
          example: data_security_plan.dmn
        created_at_in_seconds:
          type: integer
        type:
          type: string
          example: dmn
        primary:
          type: boolean
          example: false
        content_type:
          type: string
          example: text/xml
        primary_process_id:
          type: string
          example: null
        md5_hash:
          type: string
          example: f12e2bbd-a20c-673b-ccb8-a8a1ea9c5b7b

    ProcessModelFilesDiff:
      properties:
        filename:
          type: string
          example: data_security_plan.dmn
        created_at_in_seconds:
          type: integer
        type:
          type: string
          example: dmn
        primary:
          type: boolean
          example: false
        content_type:
          type: string
          example: text/xml
        primary_process_id:
          type: string
          example: null
        md5_hash:
          type: string
          example: f12e2bbd-a20c-673b-ccb8-a8a1ea9c5b7b
        location:
          type: string
          example: remote
        new:
          type: boolean
          example: false
    ProcessModelAll:
      properties:
        workflow_spec_id:
          type: string
          example: acaf1258-43b4-437e-8846-f612afa66811
        created_at_in_seconds:
          type: integer
        md5_hash:
          type: string
          example: c30fd597f21715018eab12f97f9d4956
    DataStore:
      properties:
        id:
          type: string
          example: employees
        name:
          type: string
          example: Emplyoees DataStore
        type:
          type: string
          example: TypeaheadDataStore
        description:
          type: string
          x-nullable: true
          example: This data store contains all the employees
        parent_group_id:
          type: string
          x-nullable: true
          example: Optional parent group id to specify the location of this data store
    Process:
      properties:
        identifier:
          type: string
        display_name:
          type: string
        process_group_id:
          type: string
        process_model_id:
          type: string
        type:
          type: string
        file_name:
          type: string
        has_lanes:
          type: boolean
        is_executable:
          type: boolean
        is_primary:
          type: boolean
    ProcessModel:
      properties:
        id:
          type: string
        name:
          type: string
        display_name:
          type: string
        description:
          type: string
        primary_process_id:
          type: string
          nullable: true
    ProcessGroup:
      properties:
        id:
          type: string
        name:
          type: string
        display_name:
          type: string
    File:
      properties:
        id:
          type: number
        name:
          type: string
          example: "random_fact.bpmn"
        version:
          type: integer
        updated_at_in_seconds:
          type: string
          format: date_time
          example: "2019-12-25T09:12:33.001Z"
        type:
          type: string
        primary:
          type: boolean
        content_type:
          type: string
          example: "application/xml"
        workflow_spec_id:
          type: string
          example: "random_fact"
          x-nullable: true
        file:
          type: string
    Workflow:
      properties:
        id:
          readOnly: true
          type: integer
          format: int64
        status:
          type: string
          enum: ["new", "user_input_required", "waiting", "complete"]
        navigation:
          type: array
          items:
            $ref: "#/components/schemas/NavigationItem"
        next_task:
          $ref: "#/components/schemas/Task"
        workflow_spec_id:
          type: string
        spec_version:
          type: string
        is_latest_spec:
          type: boolean
        num_tasks_total:
          type: integer
        num_tasks_complete:
          type: integer
        num_tasks_incomplete:
          type: integer

      example:
        id: 291234
        status: "user_input_required"
        workflow_spec_id: "random_fact"
        spec_version: "v1.1 [22,23]"
        is_latest_spec: True
        next_task:
          id: study_identification
          name: Study Identification
          title: IRB Review
          documentation: "# Heading 1\n\nMarkdown documentation text goes here"
          type: form
          state: ready
    TaskInstructionsForEndUser:
      properties:
        task_guid:
          type: string
        process_instance_id:
          type: integer
        instruction:
          type: string
        timestamp:
          type: number
    TaskAllowsGuest:
      properties:
        allows_guest:
          type: boolean
    Task:
      properties:
        id:
          readOnly: true
          type: string
        name:
          type: string
        title:
          type: string
        type:
          type: string
        state:
          type: string
        documentation:
          type: string
        data:
          type: object
        multi_instance_type:
          type: string
          enum: ["none", "looping", "parallel", "sequential"]
        multi_instance_count:
          type: number
        multi_instance_index:
          type: number
        process_name:
          type: string
        properties:
          type: object
      example:
        id: study_identification
        name: Study Identification
        title: IRB Review
        documentation: "# Heading 1\n\nMarkdown documentation text goes here"
        type: form
        state: ready
        form:
          "key": "irb_review_form"
          "fields":
            - "id": "irb_review_type"
              "type": "enum"
              "label": "Select IRB Review Type"
              "options":
                - id: "emergency_use"
                  name: "Emergency Use"
                - id: "humanitarian_device"
                  name: "Humanitarian Device"
                - id: "non_human"
                  name: "Non-Human"
                - id: "non_uva_agent"
                  name: "Non-UVA Agent"
                - id: "exempt"
                  name: "Exempt"
                - id: "non_engaged"
                  name: "Non-Engaged"
                - id: "expedited"
                  name: "Expedited"
                - id: "full_board"
                  name: "Full Board"
              "default_value": "Full Board"
              "validation":
                - name: "required"
                  config: "true"
              "properties":
                - id: "description"
                  value: "Description text goes here"
                - id: "help"
                  value: "# Heading 1\n\nMarkdown help text goes here"
                - id: "required_expression"
                  value: "model.my_boolean_field_id && model.my_enum_field_value !== 'something'"
                - id: "hide_expression"
                  value: "model.my_enum_field_value === 'something'"
    PaginatedTaskLog:
      properties:
        code:
          example: "email_sent"
          type: string
        level:
          example: "warning"
          type: string
        user:
          example: "email_sent"
          type: string
        page:
          type: integer
          example: 0
        per_page:
          type: integer
          example: 10
        sort_column:
          type: string
          example: "timestamp"
        sort_reverse:
          type: boolean
          example: false
        items:
          type: array
          items:
            $ref: "#/components/schemas/TaskLog"
        has_next:
          type: boolean
          example: true
        has_prev:
          type: boolean
          example: false
    TaskLog:
      properties:
        level:
          type: string
          example: "info"
        code:
          example: "email_sent"
          type: string
        message:
          example: "Approval email set to Jake in Accounting"
          type: string
        workflow_id:
          example: 42
          type: integer
        user_uid:
          example: "dhf8r"
          type: string
        timestamp:
          type: string
          format: date_time
          example: "2021-01-07T11:36:40.001Z"
    TaskEvent:
      properties:
        workflow:
          $ref: "#/components/schemas/Workflow"
        workflow_sec:
          $ref: "#/components/schemas/ProcessModel"
        spec_version:
          type: string
        action:
          type: string
        task_id:
          type: string
        task_type:
          type: string
        task_lane:
          type: string
        form_data:
          type: object
        mi_type:
          type: string
        mi_count:
          type: integer
        mi_index:
          type: integer
        process_name:
          type: string
        date:
          type: string
    Script:
      properties:
        name:
          type: string
          format: string
          example: "random_fact"
        description:
          type: string
          example: "Returns a random fact about a topic.  Provide an argument of either 'cat', 'norris', or 'buzzword'"
    LookupItem:
      properties:
        value:
          type: string
          format: string
          example: "1000"
        label:
          type: string
          example: "Chuck Norris"
        data:
          type: string
    NavigationItem:
      properties:
        id:
          type: number
          format: integer
          example: 5
        task_id:
          type: string
          format: uuid
          example: "1234123uuid1234"
        name:
          type: string
          example: "Task_Has_bananas"
        description:
          type: string
          example: "Has Bananas?"
        backtracks:
          type: boolean
          example: false
        level:
          type: integer
          example: 1
        indent:
          type: integer
          example: 2
        child_count:
          type: integer
          example: 4
        state:
          type: string
          enum:
            [
              "FUTURE",
              "WAITING",
              "READY",
              "CANCELLED",
              "COMPLETED",
              "LIKELY",
              "MAYBE",
            ]
          readOnly: true
        is_decision:
          type: boolean
          example: False
          readOnly: true
        task:
          $ref: "#/components/schemas/Task"
    Approval:
      properties:
        id:
          type: number
          format: integer
          example: 5
    ApprovalCounts:
      properties:
        PENDING:
          type: number
          format: integer
          example: 5
        APPROVED:
          type: number
          format: integer
          example: 5
        DECLINED:
          type: number
          format: integer
          example: 5
        CANCELED:
          type: number
          format: integer
          example: 5
        AWAITING:
          type: number
          format: integer
          example: 5
    ServiceTask:
      properties:
        items:
          type: array
          $ref: "#/components/schemas/ServiceTaskConnector"
          readOnly: true
    ServiceTaskConnector:
      properties:
        id:
          type: string
          example: xero/CreateInvoice
        parameters:
          type: array
          $ref: "#/components/schemas/ServiceTaskOperatorParameter"
          readOnly: true
    ServiceTaskOperatorParameter:
      properties:
        id:
          type: string
          example: client_id
        type:
          type: string
          example: str
        required:
          type: boolean
          example: false
    GitRepo:
      properties:
        #        remote:
        #          type: string
        #          example: sartography/crconnect-workflow-specs
        directory:
          type: string
          example: /home/<USER>/sync_files
        branch:
          type: string
          example: dev
        merge_branch:
          type: string
          example: staging
        changes:
          type: array
          example: ["file_1.txt", "file_2.txt"]
        untracked:
          type: array
          example: ["a_file.txt", "b_file.txt"]
    Secret:
      properties:
        key:
          description: The key of the secret we want to use
          type: string
          example: my_secret_key
          nullable: false
        value:
          description: The value associated with the key
          type: string
          example: my_super_secret_value
          nullable: false
        user_id:
          description: The id of the logged in user that updated this secret
          type: number
          example: 1
          nullable: false
    ProcessInstanceLog:
      properties:
        id:
          description: The id of the log
          type: number
          example: 1
          nullable: false
        process_instance_id:
          description: The id of the associated process instance
          type: number
          example: 2
          nullable: false
        bpmn_process_identifier:
          description: The id of the bpmn process element
          type: string
          example: Process_SimpleProcess
          nullable: false
        task:
          description: The task identifier
          type: number
          example: 1234567890
          nullable: false
        message:
          description: The msg returned in the log
          type: string
          example: Some message returned in the log
          nullable: true
        timestamp:
          description: The timestamp returned in the log
          type: number
          example: 123456789.12345

    ProcessInstanceReport:
      properties:
        id:
          type: number
          nullable: true
        identifier:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        report_metadata:
          nullable: false
          $ref: "#/components/schemas/ReportMetadata"
    # if we pass a payload and give it a field of the wrong type (say we pass a field with value null and it wants a string)
    # it will fail validation and not pass the request to the controller. that is generally not desirable
    # until we take a closer look at the schemas in here.
    AwesomeUnspecifiedPayload:
      # we know that task_submit submits no body at all, and None is not an object, as this so helpfully tells us
      # type: "object"
      additionalProperties: {}
    ReportMetadata:
      properties:
        columns:
          type: array
          nullable: false
          $ref: "#/components/schemas/ReportMetadataColumn"
        filter_by:
          type: array
          nullable: false
          $ref: "#/components/schemas/FilterValue"
        order_by:
          type: array
          nullable: false
    ReportMetadataColumn:
      properties:
        Header:
          type: string
          nullable: false
        accessor:
          type: string
          nullable: false
        fiilterable:
          type: boolean
          nullable: true
    FilterValue:
      properties:
        field_name:
          type: string
          nullable: false
        field_value:
          type: string
          nullable: false
        fiilterable:
          type: string
          nullable: false

    ServiceAccountRequest:
      properties:
        name:
          type: string
          nullable: false
    ServiceAccountApiKey:
      properties:
        api_key:
          type: string
          nullable: false
