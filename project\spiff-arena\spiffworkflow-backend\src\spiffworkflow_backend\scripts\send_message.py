"""
发送短信

创建时间: 2025-08-26 11:26:27
作者: 陈俊贤
"""
from flask import g
from typing import Any

from spiffworkflow_backend.exceptions.api_error import ApiError
from spiffworkflow_backend.models.script_attributes_context import ScriptAttributesContext
from spiffworkflow_backend.scripts.script import Script
from spiffworkflow_backend.services.sdk_message_sent_service import SMSService
from spiffworkflow_backend.services.process_instance_processor import ProcessInstanceProcessor


class SendMessage(Script):
    @staticmethod
    def requires_privileged_permissions() -> bool:
        return False
    
    def get_description(self) -> str:
        return "发送短信"
    
    def run(
        self,
        script_attributes_context: ScriptAttributesContext,
        *args: Any,
        **kwargs: Any
    ) -> Any:
        try:
            # 参数处理
            if args:
                param1 = args[0]  # 手机号
                param2 = args[1] if len(args) > 1 else {}  # 模板参数
            else:
                param1 = kwargs.get("phone_numbers")
                param2 = kwargs.get("template_params", kwargs.get("template_param", {}))

            if not param1:
                raise ValueError("phone_numbers is required")
            # 业务逻辑处理
             #   获取流程实例数据
            process_instance_id=script_attributes_context.process_instance_id,
            result = self._process_business_logic(param1, param2, process_instance_id,**kwargs)
            return result
            
        except ValueError as e:
            raise ApiError(
                error_code="invalid_parameter",
                message=f"参数错误: {str(e)}",
                status_code=400
            ) from e
        except Exception as e:
            raise ApiError(
                error_code="script_execution_error",
                message=f"脚本执行失败: {str(e)}",
                status_code=500
            ) from e
    
    def _process_business_logic(self, param1: Any, param2: Any,process_instance_id: Any, **kwargs: Any) -> Any:
        """
        私有方法：实现具体的业务逻辑

        Args:
            param1: 手机号码
            param2: 模板参数（字典或JSON字符串）
            **kwargs: 其他关键字参数

        Returns:
            Any: 处理结果
        """
        # 创建短信服务实例

        try:
            sms_service = SMSService()
            sign_name = kwargs.get('sign_name', '阿里云短信测试')
            template_code = kwargs.get('template_code', 'SMS_154950909')

            # 处理模板参数
            if isinstance(param2, dict):
                template_params = param2
            elif isinstance(param2, str):
                # 尝试解析JSON字符串
                import json
                try:
                    template_params = json.loads(param2)
                except json.JSONDecodeError:
                    # 如果不是JSON，当作普通字符串处理
                    template_params = {"content": param2}
            else:
                template_params = {"content": str(param2)}

            # 使用新的模板发送方法
            result = sms_service.send_sms_with_template(
                phone_numbers=param1,
                sign_name=sign_name,
                template_code=template_code,
                template_params=template_params,
                user_id= g.user.id,
                process_instance_id=process_instance_id
            )

            return {
                "success": result["success"],
                "message_instance_id": result["message_instance_id"],
                "content": result["content"],
                "phone_numbers": param1,
                "template_code": template_code,
                "processing_info": "短信发送完成并已记录到消息中心",
                "error": result.get("error")
            }
        except Exception as e:
            # 捕获并重新抛出异常，提供更详细的错误信息
            raise Exception(f"短信发送失败: {str(e)}") from e

