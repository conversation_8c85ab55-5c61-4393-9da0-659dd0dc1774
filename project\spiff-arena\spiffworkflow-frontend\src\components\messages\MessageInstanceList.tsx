import { useEffect, useState } from 'react';
import { ErrorOutline, Search } from '@mui/icons-material';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  Typography,
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import { Link, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import PaginationForTable from '../PaginationForTable';
import ProcessBreadcrumb from '../ProcessBreadcrumb';
import {
  getPageInfoFromSearchParams,
  modifyProcessIdentifierForPathParam,
} from '../../helpers';
import HttpService from '../../services/HttpService';
import { FormatProcessModelDisplayName } from '../MiniComponents';
import { MessageInstance } from '../../interfaces';
import DateAndTimeService from '../../services/DateAndTimeService';
import SpiffTooltip from '../SpiffTooltip';

type OwnProps = {
  processInstanceId?: number;
};

const paginationQueryParamPrefix = 'message-list';

export default function MessageInstanceList({ processInstanceId }: OwnProps) {
  const { t } = useTranslation();
  const [messageInstances, setMessageInstances] = useState([]);
  const [pagination, setPagination] = useState(null);
  const [searchParams] = useSearchParams();

  const [messageInstanceForModal, setMessageInstanceForModal] =
    useState<MessageInstance | null>(null);

  // 筛选和搜索状态
  const [messageCategory, setMessageCategory] = useState('');
  const [searchText, setSearchText] = useState('');
  const [status, setStatus] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [triggerSearch, setTriggerSearch] = useState(0); // 用于触发搜索的计数器

  useEffect(() => {
    const setMessageInstanceListFromResult = (result: any) => {
      setMessageInstances(result.results);
      setPagination(result.pagination);
    };
    const { page, perPage } = getPageInfoFromSearchParams(
      searchParams,
      undefined,
      undefined,
      paginationQueryParamPrefix,
    );
    let queryParamString = `per_page=${perPage}&page=${page}`;
    if (processInstanceId) {
      queryParamString += `&process_instance_id=${processInstanceId}`;
    }
    if (messageCategory) {
      queryParamString += `&message_category=${messageCategory}`;
    }
    if (searchText) {
      queryParamString += `&search=${encodeURIComponent(searchText)}`;
    }
    if (status) {
      queryParamString += `&status=${status}`;
    }
    if (startDate) {
      queryParamString += `&start_date=${startDate}`;
    }
    if (endDate) {
      queryParamString += `&end_date=${endDate}`;
    }

    HttpService.makeCallToBackend({
      path: `/messages?${queryParamString}`,
      successCallback: setMessageInstanceListFromResult,
    });
  }, [processInstanceId, searchParams, messageCategory, triggerSearch]);

  const handleCorrelationDisplayClose = () => {
    setMessageInstanceForModal(null);
  };

  // 处理搜索
  const handleSearch = () => {
    // 触发搜索，通过改变triggerSearch的值来触发useEffect
    setTriggerSearch(prev => prev + 1);
  };

  // 清除筛选
  const handleClearFilters = () => {
    setMessageCategory('');
    setSearchText('');
    setStatus('');
    setStartDate('');
    setEndDate('');
    // 清除后立即触发搜索
    setTriggerSearch(prev => prev + 1);
  };

  // 处理消息类型筛选变化
  const handleCategoryChange = (value: string) => {
    setMessageCategory(value);
    // 类型筛选变化后立即触发搜索
    setTriggerSearch(prev => prev + 1);
  };

  // 获取消息类型显示文本
  const getMessageCategoryText = (category: number) => {
    switch (category) {
      case 1:
        return t('message_category_internal', '站内信');
      case 2:
        return t('message_category_sms', '短信');
      case 3:
        return t('message_category_email', '邮件');
      default:
        return t('message_category_internal', '站内信');
    }
  };

  // 获取消息状态显示文本
  const getMessageStatusText = (status: string) => {
    switch (status) {
      case 'ready':
        return t('message_status_ready', '就绪');
      case 'running':
        return t('message_status_running', '运行中');
      case 'completed':
        return t('message_status_completed', '已完成');
      case 'failed':
        return t('message_status_failed', '失败');
      default:
        return status;
    }
  };

  // 获取消息类型显示文本
  const getMessageTypeText = (type: string) => {
    switch (type) {
      case 'send':
        return t('message_type_send', '发送');
      case 'receive':
        return t('message_type_receive', '接收');
      default:
        return type;
    }
  };

  // 获取消息类型颜色
  const getMessageCategoryColor = (category: number) => {
    switch (category) {
      case 1:
        return 'primary';
      case 2:
        return 'success';
      case 3:
        return 'warning';
      default:
        return 'default';
    }
  };

  const correlationsDisplayModal = () => {
    if (messageInstanceForModal) {
      let failureCausePre = null;
      if (messageInstanceForModal.failure_cause) {
        failureCausePre = (
          <>
            <Typography variant="body1" className="failure-string">
              {messageInstanceForModal.failure_cause}
            </Typography>
            <br />
          </>
        );
      }
      return (
        <Dialog
          open={!!messageInstanceForModal}
          onClose={handleCorrelationDisplayClose}
          aria-labelledby="dialog-title"
          aria-describedby="dialog-description"
        >
          <DialogTitle id="dialog-title">
            {t('message_data', {
              id: messageInstanceForModal.id,
              name: messageInstanceForModal.name,
              messageType: messageInstanceForModal.message_type,
            })}
          </DialogTitle>
          <DialogContent>
            {failureCausePre}
            {/* 显示消息内容 */}
            {messageInstanceForModal.payload && messageInstanceForModal.payload.content && (
              <>
                <DialogContentText>{t('message_content', '消息内容')}</DialogContentText>
                <Typography variant="body2" sx={{ mb: 2, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
                  {messageInstanceForModal.payload.content}
                </Typography>
              </>
            )}

            <DialogContentText>{t('correlations')}</DialogContentText>
            <pre>
              {JSON.stringify(
                messageInstanceForModal.correlation_keys,
                null,
                2,
              )}
            </pre>
          </DialogContent>
        </Dialog>
      );
    }
    return null;
  };

  const buildTable = () => {
    const rows = messageInstances.map((row: MessageInstance) => {
      let errorIcon = null;
      let errorTitle = null;
      if (row.failure_cause) {
        errorTitle = t('instance_has_error');
        errorIcon = (
          <>
            &nbsp;
            <ErrorOutline style={{ fill: 'red' }} />
          </>
        );
      }
      let processLink = <span>{t('external_call')}</span>;
      let instanceLink = <span />;
      if (row.process_instance_id != null) {
        processLink = FormatProcessModelDisplayName(row);
        instanceLink = (
          <Link
            data-qa="process-instance-show-link"
            to={`/process-instances/${modifyProcessIdentifierForPathParam(
              row.process_model_identifier,
            )}/${row.process_instance_id}`}
          >
            {row.process_instance_id}
          </Link>
        );
      }
      return (
        <TableRow key={row.id}>
          <TableCell>{row.id}</TableCell>
          <TableCell>
            <Chip
              label={getMessageCategoryText(row.message_category || 1)}
              color={getMessageCategoryColor(row.message_category || 1) as any}
              size="small"
            />
          </TableCell>
          <TableCell>{row.recipient || '-'}</TableCell>
          <TableCell>{processLink}</TableCell>
          <TableCell>{instanceLink}</TableCell>
          <TableCell>{row.name}</TableCell>
          <TableCell>{getMessageTypeText(row.message_type)}</TableCell>
          <TableCell>{row.counterpart_id}</TableCell>
          <TableCell>
            <SpiffTooltip title={errorTitle}>
              <Button
                variant="text"
                onClick={() => setMessageInstanceForModal(row)}
              >
                {t('view')}
                {errorIcon}
              </Button>
            </SpiffTooltip>
          </TableCell>
          <TableCell>
            <Chip
              label={getMessageStatusText(row.status)}
              color={row.status === 'completed' ? 'success' : row.status === 'failed' ? 'error' : 'default'}
              size="small"
            />
          </TableCell>
          <TableCell>
            {DateAndTimeService.convertSecondsToFormattedDateTime(
              row.created_at_in_seconds,
            )}
          </TableCell>
        </TableRow>
      );
    });
    return (
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('id')}</TableCell>
              <TableCell>{t('message_category_label', '消息类型')}</TableCell>
              <TableCell>{t('recipient', '接收人')}</TableCell>
              <TableCell>{t('process')}</TableCell>
              <TableCell>{t('process_instance')}</TableCell>
              <TableCell>{t('name')}</TableCell>
              <TableCell>{t('type')}</TableCell>
              <TableCell>{t('corresponding_message_instance')}</TableCell>
              <TableCell>{t('details')}</TableCell>
              <TableCell>{t('status')}</TableCell>
              <TableCell>{t('created_at')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>{rows}</TableBody>
        </Table>
      </TableContainer>
    );
  };

  if (pagination) {
    const { page, perPage } = getPageInfoFromSearchParams(
      searchParams,
      undefined,
      undefined,
      paginationQueryParamPrefix,
    );
    let breadcrumbElement = null;
    if (searchParams.get('process_instance_id')) {
      breadcrumbElement = (
        <ProcessBreadcrumb
          hotCrumbs={[
            [t('process_groups'), '/process-groups'],
            {
              entityToExplode: searchParams.get('process_model_id') || '',
              entityType: 'process-model-id',
              linkLastItem: true,
            },
            [
              t('process_instance_label', {
                id: searchParams.get('process_instance_id'),
              }),
              `/process-instances/${searchParams.get(
                'process_model_id',
              )}/${searchParams.get('process_instance_id')}`,
            ],
            [t('messages')],
          ]}
        />
      );
    }
    // 筛选和搜索组件
    const filtersComponent = (
      <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>{t('message_category_label', '消息类型')}</InputLabel>
          <Select
            value={messageCategory}
            label={t('message_category_label', '消息类型')}
            onChange={(e) => handleCategoryChange(e.target.value)}
          >
            <MenuItem value="">{t('all', '全部')}</MenuItem>
            <MenuItem value="1">{t('message_category_internal', '站内信')}</MenuItem>
            <MenuItem value="2">{t('message_category_sms', '短信')}</MenuItem>
            <MenuItem value="3">{t('message_category_email', '邮件')}</MenuItem>
          </Select>
        </FormControl>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>{t('status', '状态')}</InputLabel>
          <Select
            value={status}
            label={t('status', '状态')}
            onChange={(e) => {
              setStatus(e.target.value);
              setTriggerSearch(prev => prev + 1);
            }}
          >
            <MenuItem value="">{t('all', '全部')}</MenuItem>
            <MenuItem value="ready">{t('message_status_ready', '就绪')}</MenuItem>
            <MenuItem value="running">{t('message_status_running', '运行中')}</MenuItem>
            <MenuItem value="completed">{t('message_status_completed', '已完成')}</MenuItem>
            <MenuItem value="failed">{t('message_status_failed', '失败')}</MenuItem>
          </Select>
        </FormControl>

        <TextField
          size="small"
          type="date"
          label={t('start_date', '开始日期')}
          value={startDate}
          onChange={(e) => {
            setStartDate(e.target.value);
            setTriggerSearch(prev => prev + 1);
          }}
          slotProps={{ inputLabel: { shrink: true } }}
          sx={{ minWidth: 150 }}
        />

        <TextField
          size="small"
          type="date"
          label={t('end_date', '结束日期')}
          value={endDate}
          onChange={(e) => {
            setEndDate(e.target.value);
            setTriggerSearch(prev => prev + 1);
          }}
          slotProps={{ inputLabel: { shrink: true } }}
          sx={{ minWidth: 150 }}
        />

        <TextField
          size="small"
          label={t('search', '搜索')}
          placeholder={t('search_placeholder', '搜索接收人、消息名称或内容')}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
          sx={{ minWidth: 250 }}
        />

        <Button
          variant="contained"
          startIcon={<Search />}
          onClick={handleSearch}
          size="small"
        >
          {t('search', '搜索')}
        </Button>

        <Button
          variant="outlined"
          onClick={handleClearFilters}
          size="small"
        >
          {t('clear_filters', '清除筛选')}
        </Button>
      </Box>
    );

    return (
      <>
        {breadcrumbElement}
        {filtersComponent}
        {correlationsDisplayModal()}
        <PaginationForTable
          page={page}
          perPage={perPage}
          perPageOptions={[10, 50, 100, 500, 1000]}
          pagination={pagination}
          tableToDisplay={buildTable()}
          paginationQueryParamPrefix={paginationQueryParamPrefix}
        />
      </>
    );
  }
  return null;
}
